import { Brain, TrendingUp, Target, Award } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useRealTimeData } from "@/hooks/useRealTimeData";

export const SmartInsightsCard = () => {
  const { data, addAlert } = useRealTimeData();

  const insights = [
    {
      type: "optimization",
      icon: TrendingUp,
      title: "Peak Performance Window",
      description: "System efficiency is 15% above average. Consider running high-energy appliances now.",
      confidence: 95,
      action: "View Schedule",
    },
    {
      type: "prediction",
      icon: Target,
      title: "Monthly Goal Progress",
      description: `You're on track to exceed your monthly target by ${((data.monthlyGeneration / 950) * 100 - 100).toFixed(0)}%.`,
      confidence: 87,
      action: "Update Goals",
    },
    {
      type: "achievement",
      icon: Award,
      title: "Efficiency Milestone",
      description: "Congratulations! You've maintained 95%+ efficiency for 7 consecutive days.",
      confidence: 100,
      action: "Share Achievement",
    },
  ];

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'optimization': return 'text-blue-500';
      case 'prediction': return 'text-green-500';
      case 'achievement': return 'text-purple-500';
      default: return 'text-muted-foreground';
    }
  };

  const handleInsightAction = (insight: typeof insights[0]) => {
    addAlert({
      type: 'info',
      title: `${insight.action} Activated`,
      message: `Processing your request for: ${insight.title}`,
      read: false,
    });
  };

  return (
    <Card className="solar-card p-6">
      <div className="flex items-center gap-2 mb-6">
        <div className="p-2 bg-purple-500/10 rounded-xl">
          <Brain className="w-5 h-5 text-purple-500" />
        </div>
        <h3 className="font-heading font-semibold text-card-foreground">Smart Insights</h3>
        <Badge variant="secondary" className="ml-auto">
          AI Powered
        </Badge>
      </div>
      
      <div className="space-y-4">
        {insights.map((insight, index) => {
          const Icon = insight.icon;
          return (
            <div key={index} className="space-y-3 p-4 bg-muted/30 rounded-lg">
              <div className="flex items-start gap-3">
                <Icon className={`w-5 h-5 ${getInsightColor(insight.type)} mt-0.5`} />
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-sm">{insight.title}</h4>
                    <Badge variant="outline" className="text-xs">
                      {insight.confidence}% confidence
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground mb-3">
                    {insight.description}
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleInsightAction(insight)}
                    className="text-xs h-8"
                  >
                    {insight.action}
                  </Button>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      
      <div className="mt-4 pt-4 border-t border-border/50">
        <div className="text-xs text-muted-foreground text-center">
          Insights updated every 15 minutes using machine learning
        </div>
      </div>
    </Card>
  );
};