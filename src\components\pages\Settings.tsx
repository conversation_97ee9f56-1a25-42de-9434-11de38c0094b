import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { User, Moon, Wifi, Globe, LogOut } from "lucide-react";
import { useTheme } from "@/hooks/use-theme";
import { useToast } from "@/hooks/use-toast";

export const Settings = () => {
  const { isDark, toggleTheme } = useTheme();
  const { toast } = useToast();

  const handleThemeToggle = () => {
    toggleTheme();
    toast({
      title: `Switched to ${isDark ? 'light' : 'dark'} mode`,
      description: "Theme updated successfully",
    });
  };

  const handleLogout = () => {
    toast({
      title: "Logged out",
      description: "You have been successfully logged out",
    });
  };

  const handleLanguageChange = () => {
    toast({
      title: "Language setting",
      description: "Language preferences coming soon",
    });
  };

  const handleConfigureConnection = () => {
    toast({
      title: "IoT Configuration",
      description: "Device connection setup coming soon",
    });
  };

  const handleEditProfile = () => {
    toast({
      title: "Profile Editor",
      description: "Profile editing coming soon",
    });
  };

  return (
    <div className="p-4 pb-24 space-y-6 max-w-md mx-auto">
      <div className="text-center">
        <h2 className="text-2xl font-heading font-bold text-foreground">Settings</h2>
        <p className="text-muted-foreground">Customize your solar experience</p>
      </div>

      {/* Profile Section */}
      <Card className="solar-card p-6">
        <div className="flex items-center gap-4 mb-4">
          <div className="p-3 bg-primary/10 rounded-xl">
            <User className="w-5 h-5 text-primary" />
          </div>
          <div>
            <h3 className="font-heading font-semibold text-card-foreground">Profile</h3>
            <p className="text-sm text-muted-foreground">Manage your account</p>
          </div>
        </div>
        <Button variant="outline" className="w-full" onClick={handleEditProfile}>
          Edit Profile
        </Button>
      </Card>

      {/* Preferences */}
      <Card className="solar-card p-6 space-y-4">
        <h3 className="font-heading font-semibold text-card-foreground">Preferences</h3>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Moon className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm">Dark Mode</span>
          </div>
          <Switch checked={isDark} onCheckedChange={handleThemeToggle} />
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Globe className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm">Language</span>
          </div>
          <Button variant="ghost" size="sm" onClick={handleLanguageChange}>
            English
          </Button>
        </div>
      </Card>

      {/* System Settings */}
      <Card className="solar-card p-6">
        <div className="flex items-center gap-4 mb-4">
          <div className="p-3 bg-accent/10 rounded-xl">
            <Wifi className="w-5 h-5 text-accent" />
          </div>
          <div>
            <h3 className="font-heading font-semibold text-card-foreground">IoT Device</h3>
            <p className="text-sm text-muted-foreground">Connect your solar system</p>
          </div>
        </div>
        <Button variant="outline" className="w-full" onClick={handleConfigureConnection}>
          Configure Connection
        </Button>
      </Card>

      {/* Logout */}
      <Card className="solar-card p-4">
        <Button 
          variant="outline" 
          className="w-full text-destructive border-destructive/30 hover:bg-destructive/10"
          onClick={handleLogout}
        >
          <LogOut className="w-4 h-4 mr-2" />
          Logout
        </Button>
      </Card>
    </div>
  );
};