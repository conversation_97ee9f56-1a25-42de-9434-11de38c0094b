import { Battery, BatteryCharging, Clock, Gauge } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useRealTimeData } from "@/hooks/useRealTimeData";

export const EnhancedBatteryCard = () => {
  const { data } = useRealTimeData();
  
  const getBatteryColor = (level: number) => {
    if (level > 60) return "text-green-500";
    if (level > 30) return "text-yellow-500";
    return "text-red-500";
  };

  const getTimeRemaining = (level: number, isCharging: boolean) => {
    if (isCharging) {
      const timeToFull = ((100 - level) / 20) * 60; // Assume 20% per hour charging
      return `${Math.round(timeToFull)}min to full`;
    } else {
      const timeToEmpty = (level / 15) * 60; // Assume 15% per hour discharge
      return `${Math.round(timeToEmpty)}min remaining`;
    }
  };

  return (
    <Card className="solar-card p-6 relative overflow-hidden">
      <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-eco opacity-10 rounded-full -translate-y-4 translate-x-4"></div>
      
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-secondary/10 rounded-xl">
            {data.batteryCharging ? (
              <BatteryCharging className="w-5 h-5 text-secondary animate-pulse" />
            ) : (
              <Battery className="w-5 h-5 text-secondary" />
            )}
          </div>
          <h3 className="font-heading font-semibold text-card-foreground">Battery</h3>
        </div>
        <Gauge className="w-4 h-4 text-muted-foreground" />
      </div>
      
      <div className="space-y-4">
        <div className={`text-2xl font-mono font-bold ${getBatteryColor(data.batteryLevel)}`}>
          {Math.round(data.batteryLevel)}%
        </div>
        
        <Progress 
          value={data.batteryLevel} 
          className="h-2"
        />
        
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">
              {data.batteryCharging ? "Charging" : "Discharging"}
            </span>
            <span className="text-muted-foreground">12.5 kWh</span>
          </div>
          
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="w-3 h-3" />
            {getTimeRemaining(data.batteryLevel, data.batteryCharging)}
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4 pt-2 border-t border-border/50">
          <div>
            <span className="text-xs text-muted-foreground">Capacity</span>
            <div className="font-mono font-semibold">15.0kWh</div>
          </div>
          <div>
            <span className="text-xs text-muted-foreground">Cycles</span>
            <div className="font-mono font-semibold">1,247</div>
          </div>
        </div>
      </div>
    </Card>
  );
};