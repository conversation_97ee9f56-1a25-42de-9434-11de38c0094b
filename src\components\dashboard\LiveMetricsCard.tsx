import { Activity, Zap, Database, Wifi } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useRealTimeData } from "@/hooks/useRealTimeData";

export const LiveMetricsCard = () => {
  const { data } = useRealTimeData();

  const metrics = [
    {
      label: "System Status",
      value: "Online",
      icon: Wifi,
      status: "success" as const,
      details: `${data.totalUptime}% uptime`,
    },
    {
      label: "Data Rate",
      value: "1.2 MB/s",
      icon: Database,
      status: "info" as const,
      details: "Real-time sync",
    },
    {
      label: "Grid Connection",
      value: data.gridFlow > 0 ? "Importing" : "Exporting",
      icon: Activity,
      status: data.gridFlow > 0 ? "warning" as const : "success" as const,
      details: `${Math.abs(data.gridFlow).toFixed(1)} kW`,
    },
    {
      label: "Power Quality",
      value: "Excellent",
      icon: Zap,
      status: "success" as const,
      details: "230V / 50Hz",
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-500';
      case 'warning': return 'text-yellow-500';
      case 'info': return 'text-blue-500';
      default: return 'text-muted-foreground';
    }
  };

  return (
    <Card className="solar-card p-6">
      <div className="flex items-center gap-2 mb-6">
        <div className="p-2 bg-accent/10 rounded-xl">
          <Activity className="w-5 h-5 text-accent" />
        </div>
        <h3 className="font-heading font-semibold text-card-foreground">Live Metrics</h3>
        <Badge variant="secondary" className="ml-auto">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
          Live
        </Badge>
      </div>
      
      <div className="space-y-4">
        {metrics.map((metric, index) => {
          const Icon = metric.icon;
          return (
            <div key={index} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
              <div className="flex items-center gap-3">
                <Icon className={`w-4 h-4 ${getStatusColor(metric.status)}`} />
                <div>
                  <div className="font-medium text-sm">{metric.label}</div>
                  <div className="text-xs text-muted-foreground">{metric.details}</div>
                </div>
              </div>
              <Badge variant={metric.status === 'success' ? 'default' : 'secondary'}>
                {metric.value}
              </Badge>
            </div>
          );
        })}
      </div>
    </Card>
  );
};