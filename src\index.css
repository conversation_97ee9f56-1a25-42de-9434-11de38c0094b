@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800;900&family=Roboto:wght@300;400;500;700&family=Inter:wght@400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Premium Solar Theme Colors - HSL Format */
    --primary: 46 100% 45%; /* Golden Solar */
    --primary-foreground: 0 0% 98%;
    --primary-glow: 46 100% 65%;
    --primary-dark: 46 100% 25%;
    
    --secondary: 120 61% 34%; /* Eco Green */
    --secondary-foreground: 0 0% 98%;
    --secondary-light: 120 61% 54%;
    
    --accent: 201 100% 45%; /* Sky Blue */
    --accent-foreground: 0 0% 98%;
    --accent-light: 201 100% 65%;
    
    --success: 142 76% 36%; /* Success Green */
    --warning: 25 95% 53%; /* Energy Orange */
    --danger: 0 84% 60%; /* <PERSON><PERSON> Red */
    
    --background: 60 20% 98%; /* Warm Light BG */
    --foreground: 220 13% 18%;
    
    --card: 0 0% 100%;
    --card-foreground: 220 13% 18%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 18%;
    
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 46 100% 45%;
    
    --muted: 60 20% 94%;
    --muted-foreground: 220 13% 46%;
    
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    
    --radius: 1rem;
    
    /* Premium Solar Shadows */
    --shadow-floating: 0 20px 40px -12px rgba(251, 191, 36, 0.25);
    --shadow-card: 0 8px 24px -8px rgba(251, 191, 36, 0.15);
    --shadow-glow: 0 0 32px rgba(251, 191, 36, 0.4);
    --shadow-success: 0 8px 24px -8px rgba(34, 197, 94, 0.2);
    --shadow-accent: 0 8px 24px -8px rgba(59, 130, 246, 0.2);
    
    /* Premium Gradients */
    --gradient-solar: linear-gradient(135deg, hsl(46, 100%, 45%) 0%, hsl(46, 100%, 65%) 50%, hsl(25, 95%, 53%) 100%);
    --gradient-eco: linear-gradient(135deg, hsl(120, 61%, 34%) 0%, hsl(120, 61%, 54%) 100%);
    --gradient-sky: linear-gradient(180deg, hsl(201, 100%, 45%) 0%, hsl(201, 100%, 65%) 100%);
    --gradient-energy: linear-gradient(135deg, hsl(46, 100%, 45%) 0%, hsl(25, 95%, 53%) 50%, hsl(0, 84%, 60%) 100%);
    --gradient-bg: linear-gradient(180deg, hsl(60, 20%, 98%) 0%, hsl(46, 100%, 98%) 100%);
    
    /* Animation Durations */
    --animation-fast: 0.15s;
    --animation-normal: 0.3s;
    --animation-slow: 0.5s;
  }

  .dark {
    --background: 220 13% 9%; /* Premium Dark BG */
    --foreground: 60 20% 98%;
    
    --card: 220 13% 12%;
    --card-foreground: 60 20% 98%;
    
    --popover: 220 13% 12%;
    --popover-foreground: 60 20% 98%;
    
    --border: 220 13% 20%;
    --input: 220 13% 20%;
    
    --muted: 220 13% 15%;
    --muted-foreground: 220 13% 65%;
    
    --accent: 201 100% 45%;
    --accent-foreground: 60 20% 98%;
    
    --destructive: 0 84% 60%;
    --destructive-foreground: 60 20% 98%;
    
    /* Dark Mode Gradients */
    --gradient-bg: linear-gradient(180deg, hsl(220, 13%, 9%) 0%, hsl(220, 13%, 12%) 100%);
  }
}

@layer components {
  /* Premium Glass Effects */
  .glassmorphism {
    @apply bg-white/10 backdrop-blur-lg border border-white/20 shadow-xl;
  }
  
  .glass-dark {
    @apply bg-black/20 backdrop-blur-lg border border-white/10;
  }
  
  /* Premium Solar Cards */
  .solar-card {
    @apply relative bg-card/95 backdrop-blur-sm border border-border/60 rounded-2xl shadow-card hover:shadow-floating transition-all duration-300 overflow-hidden;
  }
  
  .solar-card::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent pointer-events-none;
  }
  
  .energy-card {
    @apply solar-card bg-gradient-to-br from-primary/10 to-secondary/5 border-primary/20;
  }
  
  .power-card {
    @apply solar-card bg-gradient-to-br from-accent/10 to-primary/5 border-accent/20;
  }
  
  .eco-card {
    @apply solar-card bg-gradient-to-br from-secondary/10 to-accent/5 border-secondary/20;
  }
  
  /* Navigation */
  .nav-item {
    @apply flex flex-col items-center justify-center p-3 rounded-xl transition-all duration-200 relative overflow-hidden;
  }
  
  .nav-item::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 transition-opacity duration-200;
  }
  
  .nav-item.active {
    @apply bg-primary/10 text-primary shadow-glow;
  }
  
  .nav-item.active::before {
    @apply opacity-100;
  }
  
  /* Premium Buttons */
  .btn-solar {
    @apply bg-gradient-solar text-white font-medium py-3 px-6 rounded-xl shadow-card hover:shadow-glow transition-all duration-300 transform hover:scale-105;
  }
  
  .btn-eco {
    @apply bg-gradient-eco text-white font-medium py-3 px-6 rounded-xl shadow-success hover:shadow-floating transition-all duration-300;
  }
  
  /* Status Indicators */
  .status-optimal {
    @apply text-success bg-success/10 border border-success/20 rounded-full px-3 py-1 text-sm font-medium;
  }
  
  .status-warning {
    @apply text-warning bg-warning/10 border border-warning/20 rounded-full px-3 py-1 text-sm font-medium;
  }
  
  .status-danger {
    @apply text-danger bg-danger/10 border border-danger/20 rounded-full px-3 py-1 text-sm font-medium;
  }
  
  /* Animated Elements */
  .pulse-glow {
    @apply animate-pulse;
    filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.6));
  }
  
  .rotating-sun {
    animation: rotate 20s linear infinite;
  }
  
  .energy-flow {
    animation: flow 2s ease-in-out infinite;
  }
  
  /* Custom Scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--primary)) transparent;
  }
  
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: hsl(var(--primary));
    border-radius: 3px;
  }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes flow {
  0%, 100% { transform: translateY(0) scale(1); opacity: 1; }
  50% { transform: translateY(-4px) scale(1.05); opacity: 0.8; }
}

@keyframes glow {
  0%, 100% { filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.6)); }
  50% { filter: drop-shadow(0 0 16px rgba(251, 191, 36, 0.8)); }
}

/* Sidebar Variables for consistency */
@layer base {
  :root {
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}