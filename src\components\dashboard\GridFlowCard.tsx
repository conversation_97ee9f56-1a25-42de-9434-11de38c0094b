import { ArrowLeftR<PERSON>, <PERSON>Up, ArrowDown } from "lucide-react";
import { Card } from "@/components/ui/card";

export const GridFlowCard = () => {
  const isExporting = true;
  const flowRate = 1.8;

  return (
    <Card className="solar-card p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-accent/10 rounded-xl">
            <ArrowLeftRight className="w-5 h-5 text-accent" />
          </div>
          <h3 className="font-heading font-semibold text-card-foreground">Grid Flow</h3>
        </div>
      </div>
      
      <div className="space-y-4">
        <div className="flex items-center justify-center">
          <div className="relative">
            <div className="w-16 h-16 border-2 border-accent/30 rounded-full flex items-center justify-center">
              {isExporting ? (
                <ArrowUp className="w-6 h-6 text-green-500 animate-bounce" />
              ) : (
                <ArrowDown className="w-6 h-6 text-orange-500" />
              )}
            </div>
          </div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-mono font-bold text-accent">
            {flowRate} <span className="text-sm text-muted-foreground">kW</span>
          </div>
          <div className="text-sm text-muted-foreground mt-1">
            {isExporting ? "Exporting to grid" : "Importing from grid"}
          </div>
        </div>
      </div>
    </Card>
  );
};