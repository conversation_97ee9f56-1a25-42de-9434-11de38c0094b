import { Battery, BatteryCharging } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

export const BatteryCard = () => {
  const batteryLevel = 78;
  const isCharging = true;

  return (
    <Card className="solar-card p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-secondary/10 rounded-xl">
            {isCharging ? (
              <BatteryCharging className="w-5 h-5 text-secondary" />
            ) : (
              <Battery className="w-5 h-5 text-secondary" />
            )}
          </div>
          <h3 className="font-heading font-semibold text-card-foreground">Battery</h3>
        </div>
      </div>
      
      <div className="space-y-4">
        <div className="text-2xl font-mono font-bold text-secondary">
          {batteryLevel}%
        </div>
        
        <Progress value={batteryLevel} className="h-2" />
        
        <div className="text-sm text-muted-foreground">
          {isCharging ? "Charging" : "Discharging"} • 2.1 kWh remaining
        </div>
      </div>
    </Card>
  );
};