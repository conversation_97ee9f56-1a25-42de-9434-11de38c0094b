import { <PERSON>, <PERSON><PERSON>, TrendingUp, TrendingDown } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useRealTimeData } from "@/hooks/useRealTimeData";

export const EnhancedPowerCard = () => {
  const { data } = useRealTimeData();
  
  const powerTrend = data.currentPower > data.averagePower;
  const powerPercentage = (data.currentPower / data.peakPower) * 100;

  return (
    <Card className="solar-card p-6 relative overflow-hidden">
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-solar opacity-10 rounded-full -translate-y-8 translate-x-8"></div>
      
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-primary/10 rounded-xl">
            <Sun className="w-5 h-5 text-primary animate-pulse" />
          </div>
          <h3 className="font-heading font-semibold text-card-foreground">Live Power</h3>
        </div>
        <div className="flex items-center gap-1">
          {powerTrend ? (
            <TrendingUp className="w-4 h-4 text-green-500" />
          ) : (
            <TrendingDown className="w-4 h-4 text-orange-500" />
          )}
          <Zap className="w-4 h-4 text-secondary" />
        </div>
      </div>
      
      <div className="space-y-4">
        <div className="text-3xl font-mono font-bold text-primary">
          {data.currentPower.toFixed(1)} <span className="text-lg text-muted-foreground">kW</span>
        </div>
        
        <Progress value={powerPercentage} className="h-2" />
        
        <div className="flex justify-between text-sm">
          <div className="flex items-center gap-2 text-muted-foreground">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            {powerTrend ? 'Above average' : 'Below average'}
          </div>
          <span className="text-muted-foreground">
            Peak: {data.peakPower.toFixed(1)}kW
          </span>
        </div>
        
        <div className="grid grid-cols-2 gap-4 pt-2 border-t border-border/50">
          <div>
            <span className="text-xs text-muted-foreground">Average</span>
            <div className="font-mono font-semibold">{data.averagePower.toFixed(1)}kW</div>
          </div>
          <div>
            <span className="text-xs text-muted-foreground">Efficiency</span>
            <div className="font-mono font-semibold text-green-500">{data.efficiency.toFixed(1)}%</div>
          </div>
        </div>
      </div>
    </Card>
  );
};