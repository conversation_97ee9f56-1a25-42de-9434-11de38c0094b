import { Sun, Zap } from "lucide-react";
import { Card } from "@/components/ui/card";

export const PowerCard = () => {
  return (
    <Card className="solar-card p-6 relative overflow-hidden">
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-solar opacity-10 rounded-full -translate-y-8 translate-x-8"></div>
      
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-primary/10 rounded-xl">
            <Sun className="w-5 h-5 text-primary animate-pulse" />
          </div>
          <h3 className="font-heading font-semibold text-card-foreground">Live Power</h3>
        </div>
        <Zap className="w-4 h-4 text-secondary" />
      </div>
      
      <div className="space-y-2">
        <div className="text-3xl font-mono font-bold text-primary">
          4.2 <span className="text-lg text-muted-foreground">kW</span>
        </div>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          Optimal generation
        </div>
      </div>
    </Card>
  );
};