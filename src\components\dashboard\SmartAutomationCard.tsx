import { useState } from "react";
import { Bot, Lightbulb, Zap, Clock, Smartphone, Home, Battery } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { useRealTimeData } from "@/hooks/useRealTimeData";

export const SmartAutomationCard = () => {
  const { toast } = useToast();
  const { data } = useRealTimeData();
  const [automations, setAutomations] = useState({
    smartCharging: true,
    peakShaving: true,
    weatherOptimization: false,
    loadBalancing: true,
    backupMode: false,
  });

  const activeRules = [
    {
      name: "Smart EV Charging",
      description: "Charge vehicle during peak solar hours",
      status: "Active",
      savings: "$45/month",
      icon: Zap,
      enabled: automations.smartCharging,
    },
    {
      name: "Peak Shaving",
      description: "Use battery during high grid rates",
      status: "Scheduled",
      savings: "$67/month",
      icon: Battery,
      enabled: automations.peakShaving,
    },
    {
      name: "Smart Appliances",
      description: "Run dishwasher during solar peak",
      status: "Waiting",
      savings: "$23/month",
      icon: Home,
      enabled: automations.loadBalancing,
    },
  ];

  const toggleAutomation = (key: string) => {
    setAutomations(prev => {
      const newState = { ...prev, [key]: !prev[key] };
      toast({
        title: `Automation ${newState[key] ? 'Enabled' : 'Disabled'}`,
        description: `${key.replace(/([A-Z])/g, ' $1').trim()} has been ${newState[key] ? 'turned on' : 'turned off'}.`,
      });
      return newState;
    });
  };

  const createNewRule = () => {
    toast({
      title: "New Automation Rule",
      description: "Opening automation wizard...",
    });
  };

  return (
    <Card className="solar-card p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Bot className="w-6 h-6 text-primary" />
          <h3 className="font-heading font-bold text-xl">Smart Automation</h3>
        </div>
        <Badge variant="secondary">AI Powered</Badge>
      </div>

      {/* Active Rules */}
      <div className="space-y-4 mb-6">
        <h4 className="font-semibold flex items-center gap-2">
          <Lightbulb className="w-4 h-4 text-yellow-500" />
          Active Rules
        </h4>
        
        {activeRules.map((rule, index) => (
          <div key={index} className="p-4 bg-muted/30 rounded-lg">
            <div className="flex items-start justify-between mb-2">
              <div className="flex items-center gap-2">
                <rule.icon className="w-5 h-5 text-primary" />
                <div>
                  <div className="font-medium">{rule.name}</div>
                  <div className="text-xs text-muted-foreground">{rule.description}</div>
                </div>
              </div>
              <Switch 
                checked={rule.enabled}
                onCheckedChange={() => toggleAutomation(rule.name.toLowerCase().replace(/\s+/g, ''))}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Badge 
                variant={rule.status === "Active" ? "default" : rule.status === "Scheduled" ? "secondary" : "outline"}
                className="text-xs"
              >
                {rule.status}
              </Badge>
              <span className="text-sm font-mono font-bold text-green-500">
                {rule.savings}
              </span>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Toggles */}
      <div className="space-y-3 mb-6">
        <h4 className="font-semibold">Quick Controls</h4>
        
        <div className="grid grid-cols-1 gap-3">
          <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              <span className="text-sm font-medium">Weather Optimization</span>
            </div>
            <Switch 
              checked={automations.weatherOptimization}
              onCheckedChange={() => toggleAutomation('weatherOptimization')}
            />
          </div>

          <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
            <div className="flex items-center gap-2">
              <Smartphone className="w-4 h-4" />
              <span className="text-sm font-medium">Backup Mode</span>
            </div>
            <Switch 
              checked={automations.backupMode}
              onCheckedChange={() => toggleAutomation('backupMode')}
            />
          </div>
        </div>
      </div>

      {/* Today's Automations */}
      <div className="space-y-3 mb-6">
        <h4 className="font-semibold">Today's Schedule</h4>
        
        <div className="space-y-2">
          <div className="flex justify-between items-center text-sm">
            <span>11:00 AM - EV Charging Start</span>
            <Badge variant="outline" className="text-xs">Scheduled</Badge>
          </div>
          <div className="flex justify-between items-center text-sm">
            <span>2:00 PM - Dishwasher Run</span>
            <Badge variant="outline" className="text-xs">Pending</Badge>
          </div>
          <div className="flex justify-between items-center text-sm">
            <span>6:00 PM - Peak Shaving Active</span>
            <Badge variant="outline" className="text-xs">Ready</Badge>
          </div>
        </div>
      </div>

      {/* Energy Savings */}
      <div className="p-4 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg border border-green-500/20 mb-4">
        <div className="flex justify-between items-center mb-2">
          <span className="font-medium">Monthly Savings</span>
          <span className="font-mono font-bold text-green-500">$135</span>
        </div>
        <Progress value={78} className="h-2" />
        <div className="text-xs text-muted-foreground mt-1">
          78% of target achieved
        </div>
      </div>

      <Button 
        onClick={createNewRule}
        className="w-full"
        variant="outline"
      >
        Create New Automation Rule
      </Button>
    </Card>
  );
};