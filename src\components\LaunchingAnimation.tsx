import { useEffect, useState } from "react";
import { Sun, Zap, Battery } from "lucide-react";

interface LaunchingAnimationProps {
  onComplete: () => void;
}

export const LaunchingAnimation = ({ onComplete }: LaunchingAnimationProps) => {
  const [progress, setProgress] = useState(0);
  const [phase, setPhase] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setTimeout(onComplete, 500);
          return 100;
        }
        return prev + 2;
      });
    }, 50);

    const phaseInterval = setInterval(() => {
      setPhase(prev => (prev + 1) % 3);
    }, 800);

    return () => {
      clearInterval(interval);
      clearInterval(phaseInterval);
    };
  }, [onComplete]);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-primary/5 via-background to-primary/10">
      <div className="relative">
        {/* Background glow */}
        <div className="absolute inset-0 animate-pulse">
          <div className="w-32 h-32 mx-auto rounded-full bg-gradient-to-r from-primary/20 to-primary/40 blur-xl"></div>
        </div>
        
        {/* Main container */}
        <div className="relative z-10 text-center space-y-8">
          {/* Solar icon with rotation */}
          <div className="relative w-20 h-20 mx-auto">
            <Sun 
              className={`w-20 h-20 text-primary transition-all duration-1000 ${
                phase === 0 ? 'animate-spin scale-100 opacity-100' : 
                phase === 1 ? 'scale-110 opacity-80' : 'scale-95 opacity-60'
              }`} 
            />
            
            {/* Energy particles */}
            <div className="absolute inset-0">
              <Zap 
                className={`absolute w-4 h-4 text-primary/60 transition-all duration-700 ${
                  phase === 1 ? 'top-2 right-2 animate-bounce' : 'top-8 right-8 opacity-0'
                }`} 
              />
              <Battery 
                className={`absolute w-4 h-4 text-primary/60 transition-all duration-700 ${
                  phase === 2 ? 'bottom-2 left-2 animate-pulse' : 'bottom-8 left-8 opacity-0'
                }`} 
              />
            </div>
          </div>

          {/* App title */}
          <div className="space-y-2">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent animate-fade-in">
              Smart Solar
            </h1>
            <p className="text-muted-foreground animate-fade-in">
              Power Your Future
            </p>
          </div>

          {/* Progress bar */}
          <div className="w-64 mx-auto space-y-2">
            <div className="h-1 bg-muted rounded-full overflow-hidden">
              <div 
                className="h-full bg-gradient-to-r from-primary to-primary/80 transition-all duration-300 ease-out rounded-full"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-xs text-muted-foreground">
              {progress < 30 ? 'Initializing solar panels...' :
               progress < 60 ? 'Connecting to grid...' :
               progress < 90 ? 'Loading analytics...' :
               'Ready to power up!'}
            </p>
          </div>

          {/* Loading dots */}
          <div className="flex justify-center space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={`w-2 h-2 bg-primary rounded-full transition-all duration-300 ${
                  Math.floor(progress / 10) % 3 === i ? 'animate-bounce' : 'opacity-50'
                }`}
              ></div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};