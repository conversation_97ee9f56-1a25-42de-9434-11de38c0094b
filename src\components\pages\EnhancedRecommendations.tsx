import { useState } from "react";
import { Lightbulb, Star, CheckCircle, Clock, Target, TrendingUp, Leaf, DollarSign } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useRealTimeData } from "@/hooks/useRealTimeData";
import { useToast } from "@/hooks/use-toast";
import { ProfessionalButton } from "@/components/ui/professional-button";

export const EnhancedRecommendations = () => {
  const { data, addAlert } = useRealTimeData();
  const { toast } = useToast();
  const [implementedTips, setImplementedTips] = useState<string[]>([]);

  const recommendations = {
    optimization: [
      {
        id: "opt1",
        title: "Peak Hour Utilization",
        description: "Run dishwasher and washing machine between 11 AM - 2 PM when solar generation is highest",
        impact: "15% efficiency boost",
        difficulty: "Easy",
        savings: "$25/month",
        category: "timing",
        priority: "high",
        timeToImplement: "Immediate",
        steps: [
          "Set appliance timers for peak solar hours",
          "Use delay start features on major appliances",
          "Monitor energy usage patterns"
        ]
      },
      {
        id: "opt2",
        title: "Battery Optimization",
        description: "Adjust battery discharge schedule to maximize solar energy usage",
        impact: "12% cost reduction",
        difficulty: "Medium",
        savings: "$30/month",
        category: "battery",
        priority: "high",
        timeToImplement: "2 days",
        steps: [
          "Access battery management system",
          "Set discharge times to off-peak hours",
          "Configure minimum charge threshold"
        ]
      },
      {
        id: "opt3",
        title: "Panel Cleaning Schedule",
        description: "Clean solar panels monthly to maintain optimal efficiency",
        impact: "8% generation increase",
        difficulty: "Easy",
        savings: "$15/month",
        category: "maintenance",
        priority: "medium",
        timeToImplement: "Weekly",
        steps: [
          "Use soft brush and distilled water",
          "Clean early morning or late afternoon",
          "Check for debris and bird droppings"
        ]
      }
    ],
    seasonal: [
      {
        id: "sea1",
        title: "Winter Preparation",
        description: "Optimize system for shorter daylight hours and potential snow coverage",
        impact: "Maintain 85% efficiency",
        difficulty: "Medium",
        savings: "$40/month",
        category: "seasonal",
        priority: "medium",
        timeToImplement: "1 week",
        steps: [
          "Install snow guards if needed",
          "Adjust battery charge schedule",
          "Increase backup power reserves"
        ]
      },
      {
        id: "sea2",
        title: "Summer Cooling Strategy",
        description: "Use excess solar power for air conditioning during peak generation",
        impact: "20% cost savings",
        difficulty: "Easy",
        savings: "$60/month",
        category: "cooling",
        priority: "high",
        timeToImplement: "Immediate",
        steps: [
          "Pre-cool home during peak solar hours",
          "Set AC timer to solar generation schedule",
          "Use thermal mass to store cooling"
        ]
      }
    ],
    financial: [
      {
        id: "fin1",
        title: "Net Metering Optimization",
        description: "Maximize grid feed-in during peak rate periods",
        impact: "25% revenue increase",
        difficulty: "Hard",
        savings: "$80/month",
        category: "grid",
        priority: "high",
        timeToImplement: "1 month",
        steps: [
          "Analyze utility rate structures",
          "Configure export timing",
          "Track peak rate periods"
        ]
      },
      {
        id: "fin2",
        title: "Tax Incentive Tracking",
        description: "Document all solar-related expenses for maximum tax benefits",
        impact: "Up to $2000 annually",
        difficulty: "Easy",
        savings: "$167/month",
        category: "taxes",
        priority: "medium",
        timeToImplement: "Ongoing",
        steps: [
          "Keep all installation receipts",
          "Track maintenance costs",
          "Consult tax professional"
        ]
      }
    ]
  };

  const achievements = [
    { name: "Early Adopter", description: "Implemented 5 recommendations", progress: 60 },
    { name: "Efficiency Expert", description: "Achieved 95%+ efficiency", progress: 100 },
    { name: "Cost Saver", description: "Saved $500+ annually", progress: 80 },
    { name: "Green Champion", description: "Prevented 1 ton CO2", progress: 90 },
  ];

  const implementTip = (tipId: string, tipTitle: string) => {
    if (!implementedTips.includes(tipId)) {
      setImplementedTips([...implementedTips, tipId]);
      addAlert({
        type: 'success',
        title: 'Tip Implemented!',
        message: `You've successfully implemented: ${tipTitle}`,
        read: false,
      });
      toast({
        title: "Great work!",
        description: `${tipTitle} has been marked as implemented.`,
      });
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-500 bg-red-500/10';
      case 'medium': return 'text-yellow-500 bg-yellow-500/10';
      case 'low': return 'text-green-500 bg-green-500/10';
      default: return 'text-gray-500 bg-gray-500/10';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'text-green-500';
      case 'Medium': return 'text-yellow-500';
      case 'Hard': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'timing': return <Clock className="w-4 h-4" />;
      case 'battery': return <Target className="w-4 h-4" />;
      case 'maintenance': return <CheckCircle className="w-4 h-4" />;
      case 'seasonal': return <Leaf className="w-4 h-4" />;
      case 'grid': return <TrendingUp className="w-4 h-4" />;
      case 'taxes': return <DollarSign className="w-4 h-4" />;
      default: return <Lightbulb className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-bg">
      <div className="p-4 pb-24 space-y-6 max-w-md mx-auto">
        {/* Header */}
        <Card className="solar-card p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 bg-gradient-premium rounded-xl">
              <Lightbulb className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="font-heading font-bold text-xl">Smart Recommendations</h1>
              <p className="text-sm text-muted-foreground">AI-powered optimization tips</p>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="font-mono font-bold text-2xl text-primary">
                {implementedTips.length}
              </div>
              <div className="text-xs text-muted-foreground">Implemented</div>
            </div>
            <div className="text-center">
              <div className="font-mono font-bold text-2xl text-green-500">
                ${(implementedTips.length * 35).toFixed(0)}
              </div>
              <div className="text-xs text-muted-foreground">Monthly Savings</div>
            </div>
            <div className="text-center">
              <div className="font-mono font-bold text-2xl text-secondary">
                {(implementedTips.length * 12).toFixed(0)}%
              </div>
              <div className="text-xs text-muted-foreground">Efficiency Gain</div>
            </div>
          </div>
        </Card>

        {/* Achievement Progress */}
        <Card className="solar-card p-6">
          <h3 className="font-heading font-semibold mb-4 flex items-center gap-2">
            <Star className="w-5 h-5 text-primary" />
            Your Progress
          </h3>
          
          <div className="space-y-3">
            {achievements.map((achievement, index) => (
              <div key={index} className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-sm">{achievement.name}</span>
                  <span className="text-xs text-muted-foreground">
                    {achievement.progress}%
                  </span>
                </div>
                <Progress value={achievement.progress} className="h-2" />
                <p className="text-xs text-muted-foreground">
                  {achievement.description}
                </p>
              </div>
            ))}
          </div>
        </Card>

        {/* Recommendations Tabs */}
        <Tabs defaultValue="optimization" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="optimization">Optimize</TabsTrigger>
            <TabsTrigger value="seasonal">Seasonal</TabsTrigger>
            <TabsTrigger value="financial">Financial</TabsTrigger>
          </TabsList>

          <TabsContent value="optimization">
            <div className="space-y-4">
              {recommendations.optimization.map((tip) => (
                <Card key={tip.id} className="solar-card p-6">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        {getCategoryIcon(tip.category)}
                      </div>
                      <div>
                        <h3 className="font-medium text-sm">{tip.title}</h3>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge className={`text-xs ${getPriorityColor(tip.priority)}`}>
                            {tip.priority} priority
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {tip.timeToImplement}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    {implementedTips.includes(tip.id) ? (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    ) : (
                      <ProfessionalButton
                        variant="outline"
                        size="sm"
                        onClick={() => implementTip(tip.id, tip.title)}
                      >
                        Implement
                      </ProfessionalButton>
                    )}
                  </div>

                  <p className="text-sm text-muted-foreground mb-4">
                    {tip.description}
                  </p>

                  <div className="grid grid-cols-3 gap-3 mb-4">
                    <div className="text-center p-2 bg-muted/30 rounded-lg">
                      <div className="text-xs text-muted-foreground">Impact</div>
                      <div className="font-medium text-xs text-primary">{tip.impact}</div>
                    </div>
                    <div className="text-center p-2 bg-muted/30 rounded-lg">
                      <div className="text-xs text-muted-foreground">Difficulty</div>
                      <div className={`font-medium text-xs ${getDifficultyColor(tip.difficulty)}`}>
                        {tip.difficulty}
                      </div>
                    </div>
                    <div className="text-center p-2 bg-muted/30 rounded-lg">
                      <div className="text-xs text-muted-foreground">Savings</div>
                      <div className="font-medium text-xs text-green-500">{tip.savings}</div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="text-xs font-medium text-muted-foreground">
                      Implementation Steps:
                    </div>
                    {tip.steps.map((step, index) => (
                      <div key={index} className="flex items-center gap-2 text-xs">
                        <div className="w-4 h-4 rounded-full bg-primary/20 text-primary flex items-center justify-center font-bold">
                          {index + 1}
                        </div>
                        <span className="text-muted-foreground">{step}</span>
                      </div>
                    ))}
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="seasonal">
            <div className="space-y-4">
              {recommendations.seasonal.map((tip) => (
                <Card key={tip.id} className="solar-card p-6">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <div className="p-2 bg-secondary/10 rounded-lg">
                        {getCategoryIcon(tip.category)}
                      </div>
                      <div>
                        <h3 className="font-medium text-sm">{tip.title}</h3>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge className={`text-xs ${getPriorityColor(tip.priority)}`}>
                            {tip.priority} priority
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {tip.timeToImplement}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    {implementedTips.includes(tip.id) ? (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    ) : (
                      <ProfessionalButton
                        variant="secondary"
                        size="sm"
                        onClick={() => implementTip(tip.id, tip.title)}
                      >
                        Implement
                      </ProfessionalButton>
                    )}
                  </div>

                  <p className="text-sm text-muted-foreground mb-4">
                    {tip.description}
                  </p>

                  <div className="grid grid-cols-3 gap-3 mb-4">
                    <div className="text-center p-2 bg-muted/30 rounded-lg">
                      <div className="text-xs text-muted-foreground">Impact</div>
                      <div className="font-medium text-xs text-secondary">{tip.impact}</div>
                    </div>
                    <div className="text-center p-2 bg-muted/30 rounded-lg">
                      <div className="text-xs text-muted-foreground">Difficulty</div>
                      <div className={`font-medium text-xs ${getDifficultyColor(tip.difficulty)}`}>
                        {tip.difficulty}
                      </div>
                    </div>
                    <div className="text-center p-2 bg-muted/30 rounded-lg">
                      <div className="text-xs text-muted-foreground">Savings</div>
                      <div className="font-medium text-xs text-green-500">{tip.savings}</div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="text-xs font-medium text-muted-foreground">
                      Implementation Steps:
                    </div>
                    {tip.steps.map((step, index) => (
                      <div key={index} className="flex items-center gap-2 text-xs">
                        <div className="w-4 h-4 rounded-full bg-secondary/20 text-secondary flex items-center justify-center font-bold">
                          {index + 1}
                        </div>
                        <span className="text-muted-foreground">{step}</span>
                      </div>
                    ))}
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="financial">
            <div className="space-y-4">
              {recommendations.financial.map((tip) => (
                <Card key={tip.id} className="solar-card p-6">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <div className="p-2 bg-accent/10 rounded-lg">
                        {getCategoryIcon(tip.category)}
                      </div>
                      <div>
                        <h3 className="font-medium text-sm">{tip.title}</h3>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge className={`text-xs ${getPriorityColor(tip.priority)}`}>
                            {tip.priority} priority
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {tip.timeToImplement}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    {implementedTips.includes(tip.id) ? (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    ) : (
                      <ProfessionalButton
                        variant="accent"
                        size="sm"
                        onClick={() => implementTip(tip.id, tip.title)}
                      >
                        Implement
                      </ProfessionalButton>
                    )}
                  </div>

                  <p className="text-sm text-muted-foreground mb-4">
                    {tip.description}
                  </p>

                  <div className="grid grid-cols-3 gap-3 mb-4">
                    <div className="text-center p-2 bg-muted/30 rounded-lg">
                      <div className="text-xs text-muted-foreground">Impact</div>
                      <div className="font-medium text-xs text-accent">{tip.impact}</div>
                    </div>
                    <div className="text-center p-2 bg-muted/30 rounded-lg">
                      <div className="text-xs text-muted-foreground">Difficulty</div>
                      <div className={`font-medium text-xs ${getDifficultyColor(tip.difficulty)}`}>
                        {tip.difficulty}
                      </div>
                    </div>
                    <div className="text-center p-2 bg-muted/30 rounded-lg">
                      <div className="text-xs text-muted-foreground">Savings</div>
                      <div className="font-medium text-xs text-green-500">{tip.savings}</div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="text-xs font-medium text-muted-foreground">
                      Implementation Steps:
                    </div>
                    {tip.steps.map((step, index) => (
                      <div key={index} className="flex items-center gap-2 text-xs">
                        <div className="w-4 h-4 rounded-full bg-accent/20 text-accent flex items-center justify-center font-bold">
                          {index + 1}
                        </div>
                        <span className="text-muted-foreground">{step}</span>
                      </div>
                    ))}
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};