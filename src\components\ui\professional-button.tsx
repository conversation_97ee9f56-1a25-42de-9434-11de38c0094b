import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const professionalButtonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        primary: "bg-gradient-solar text-white shadow-glow hover:shadow-lg hover:scale-105 active:scale-95",
        secondary: "bg-gradient-eco text-white shadow-success hover:shadow-lg hover:scale-105 active:scale-95",
        accent: "bg-gradient-sky text-white shadow-accent hover:shadow-lg hover:scale-105 active:scale-95",
        premium: "bg-gradient-premium text-white shadow-premium hover:shadow-lg hover:scale-105 active:scale-95",
        glass: "bg-gradient-glass backdrop-blur-md border border-white/20 text-foreground hover:bg-white/10 shadow-glass",
        outline: "border-2 border-primary text-primary bg-transparent hover:bg-primary hover:text-white shadow-card hover:shadow-glow",
        ghost: "text-foreground hover:bg-accent/10 hover:text-accent-foreground",
      },
      size: {
        sm: "h-8 px-3 text-xs",
        default: "h-10 px-4 py-2",
        lg: "h-12 px-6 text-base",
        xl: "h-14 px-8 text-lg",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "default",
    },
  }
);

export interface ProfessionalButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof professionalButtonVariants> {
  asChild?: boolean;
}

const ProfessionalButton = React.forwardRef<HTMLButtonElement, ProfessionalButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(professionalButtonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
ProfessionalButton.displayName = "ProfessionalButton";

export { ProfessionalButton, professionalButtonVariants };