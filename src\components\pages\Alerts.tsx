import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, CheckCircle, Clock, Wrench } from "lucide-react";

export const Alerts = () => {
  const alerts = [
    {
      id: 1,
      type: "warning",
      title: "Low Performance Detected",
      description: "Panel efficiency down 8% - cleaning recommended",
      time: "2 hours ago",
      status: "active",
      action: "Schedule Cleaning"
    },
    {
      id: 2,
      type: "info",
      title: "Weather Alert",
      description: "Heavy clouds expected - reduced generation likely",
      time: "4 hours ago",
      status: "active",
      action: "View Forecast"
    },
    {
      id: 3,
      type: "success",
      title: "Maintenance Complete",
      description: "Inverter check completed successfully",
      time: "1 day ago",
      status: "resolved",
      action: "View Report"
    }
  ];

  const getIcon = (type: string) => {
    switch (type) {
      case "warning":
        return <AlertTriangle className="w-5 h-5 text-orange-500" />;
      case "success":
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      default:
        return <Clock className="w-5 h-5 text-blue-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    return status === "active" ? "default" : "secondary";
  };

  return (
    <div className="p-4 pb-24 space-y-6 max-w-md mx-auto">
      <div className="text-center">
        <h2 className="text-2xl font-heading font-bold text-foreground">Alerts & Notifications</h2>
        <p className="text-muted-foreground">Stay informed about your system</p>
      </div>

      <div className="space-y-4">
        {alerts.map((alert) => (
          <Card key={alert.id} className="solar-card p-6">
            <div className="flex items-start gap-4">
              <div className="p-2 bg-muted/50 rounded-lg">
                {getIcon(alert.type)}
              </div>
              <div className="flex-1 space-y-3">
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-heading font-semibold text-card-foreground">
                      {alert.title}
                    </h3>
                    <Badge variant={getStatusColor(alert.status)} className="text-xs">
                      {alert.status}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {alert.description}
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    {alert.time}
                  </p>
                </div>
                <Button size="sm" variant="outline" className="w-full">
                  {alert.action}
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>

      <Card className="solar-card p-6 bg-gradient-to-br from-accent/5 to-primary/5">
        <div className="flex items-center gap-3 mb-4">
          <Wrench className="w-6 h-6 text-accent" />
          <h3 className="font-heading font-semibold text-foreground">Book Service</h3>
        </div>
        <p className="text-sm text-muted-foreground mb-4">
          Need professional maintenance? Connect with certified technicians
        </p>
        <Button className="w-full" variant="outline">
          Schedule Maintenance
        </Button>
      </Card>
    </div>
  );
};