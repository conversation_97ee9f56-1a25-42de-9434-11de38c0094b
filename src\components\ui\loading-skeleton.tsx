import { cn } from "@/lib/utils";

interface LoadingSkeletonProps {
  className?: string;
  variant?: "card" | "text" | "avatar" | "button";
}

export const LoadingSkeleton = ({ className, variant = "card" }: LoadingSkeletonProps) => {
  const baseClasses = "animate-pulse bg-muted rounded-lg";
  
  const variantClasses = {
    card: "h-32 w-full",
    text: "h-4 w-full",
    avatar: "h-12 w-12 rounded-full",
    button: "h-10 w-24",
  };

  return (
    <div className={cn(baseClasses, variantClasses[variant], className)} />
  );
};

export const DashboardSkeleton = () => {
  return (
    <div className="space-y-6 p-4">
      <div className="text-center space-y-4">
        <LoadingSkeleton className="h-8 w-48 mx-auto" variant="text" />
        <LoadingSkeleton className="h-4 w-64 mx-auto" variant="text" />
      </div>
      
      <LoadingSkeleton className="h-40" />
      
      <div className="grid grid-cols-2 gap-4">
        <LoadingSkeleton />
        <LoadingSkeleton />
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <LoadingSkeleton />
        <LoadingSkeleton />
      </div>
      
      <div className="space-y-4">
        <LoadingSkeleton className="h-24" />
        <LoadingSkeleton className="h-24" />
      </div>
    </div>
  );
};