import { Cloud, Sun, CloudRain, Wind, Droplets, Thermometer } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useRealTimeData } from "@/hooks/useRealTimeData";

export const WeatherInsightsCard = () => {
  const { data } = useRealTimeData();

  const getWeatherIcon = (condition: string) => {
    switch (condition) {
      case 'sunny': return <Sun className="w-6 h-6 text-yellow-500" />;
      case 'cloudy': return <Cloud className="w-6 h-6 text-gray-500" />;
      case 'rainy': return <CloudRain className="w-6 h-6 text-blue-500" />;
      default: return <Sun className="w-6 h-6 text-yellow-500" />;
    }
  };

  const getUVLevel = (index: number) => {
    if (index <= 2) return { level: 'Low', color: 'text-green-500' };
    if (index <= 5) return { level: 'Moderate', color: 'text-yellow-500' };
    if (index <= 7) return { level: 'High', color: 'text-orange-500' };
    return { level: 'Very High', color: 'text-red-500' };
  };

  const uvInfo = getUVLevel(data.weather.uvIndex);

  return (
    <Card className="solar-card p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-blue-500/10 rounded-xl">
            {getWeatherIcon(data.weather.condition)}
          </div>
          <h3 className="font-heading font-semibold text-card-foreground">Weather Insights</h3>
        </div>
      </div>
      
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Thermometer className="w-4 h-4 text-orange-500" />
            <span className="text-sm font-medium">Temperature</span>
          </div>
          <span className="font-mono font-semibold">{data.weather.temperature}°C</span>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Droplets className="w-4 h-4 text-blue-500" />
            <span className="text-sm font-medium">Humidity</span>
          </div>
          <span className="font-mono font-semibold">{data.weather.humidity}%</span>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Sun className="w-4 h-4 text-yellow-500" />
              <span className="text-sm font-medium">UV Index</span>
            </div>
            <span className={`font-mono font-semibold ${uvInfo.color}`}>
              {data.weather.uvIndex} ({uvInfo.level})
            </span>
          </div>
          <Progress value={(data.weather.uvIndex / 11) * 100} className="h-2" />
        </div>

        <div className="pt-4 border-t border-border/50">
          <div className="text-xs text-muted-foreground mb-2">Solar Generation Forecast</div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-green-500">
              Optimal conditions for next 4 hours
            </span>
          </div>
          <div className="text-xs text-muted-foreground mt-1">
            Expected: +15% above average generation
          </div>
        </div>
      </div>
    </Card>
  );
};