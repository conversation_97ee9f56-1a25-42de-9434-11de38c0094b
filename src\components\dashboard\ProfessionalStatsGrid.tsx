import { TrendingUp, DollarSign, Leaf, Zap, Clock, Target } from "lucide-react";
import { Card } from "@/components/ui/card";
import { useRealTimeData } from "@/hooks/useRealTimeData";

export const ProfessionalStatsGrid = () => {
  const { data } = useRealTimeData();

  const stats = [
    {
      label: "Today's Generation",
      value: `${data.dailyGeneration.toFixed(1)} kWh`,
      change: "+12.5%",
      trend: "up",
      icon: Zap,
      color: "text-primary",
      bgColor: "bg-primary/10",
    },
    {
      label: "Monthly Savings",
      value: `$${data.moneySaved.toFixed(0)}`,
      change: "+8.3%",
      trend: "up",
      icon: DollarSign,
      color: "text-green-500",
      bgColor: "bg-green-500/10",
    },
    {
      label: "CO₂ Avoided",
      value: `${data.co2Saved.toFixed(1)} kg`,
      change: "+15.2%",
      trend: "up",
      icon: Leaf,
      color: "text-secondary",
      bgColor: "bg-secondary/10",
    },
    {
      label: "System Uptime",
      value: `${data.totalUptime.toFixed(1)}%`,
      change: "99.9%",
      trend: "stable",
      icon: Clock,
      color: "text-blue-500",
      bgColor: "bg-blue-500/10",
    },
    {
      label: "Peak Efficiency",
      value: `${data.efficiency.toFixed(1)}%`,
      change: "+2.1%",
      trend: "up",
      icon: Target,
      color: "text-purple-500",
      bgColor: "bg-purple-500/10",
    },
    {
      label: "Weekly Total",
      value: `${data.weeklyGeneration.toFixed(0)} kWh`,
      change: "+5.7%",
      trend: "up",
      icon: TrendingUp,
      color: "text-accent",
      bgColor: "bg-accent/10",
    },
  ];

  return (
    <Card className="solar-card p-6">
      <h3 className="font-heading font-semibold text-lg mb-6 text-center">
        Performance Overview
      </h3>
      
      <div className="grid grid-cols-2 gap-4">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div
              key={index}
              className="group p-4 rounded-xl bg-gradient-to-br from-background to-muted/30 border border-border/50 hover:border-primary/30 transition-all duration-200 hover:shadow-card hover:scale-105"
            >
              <div className="flex items-center gap-3 mb-3">
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <Icon className={`w-4 h-4 ${stat.color}`} />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-xs text-muted-foreground font-medium uppercase tracking-wide">
                    {stat.label}
                  </div>
                </div>
              </div>
              
              <div className="space-y-1">
                <div className="font-mono font-bold text-lg text-foreground">
                  {stat.value}
                </div>
                <div className={`text-xs font-medium flex items-center gap-1 ${
                  stat.trend === 'up' ? 'text-green-500' : 
                  stat.trend === 'down' ? 'text-red-500' : 
                  'text-muted-foreground'
                }`}>
                  {stat.trend === 'up' && <TrendingUp className="w-3 h-3" />}
                  {stat.change}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </Card>
  );
};