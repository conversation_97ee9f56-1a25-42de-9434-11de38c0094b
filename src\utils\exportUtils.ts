// Export utilities for reports and data

export const exportToPDF = (data: any) => {
  try {
    // Create a detailed report content
    const reportContent = `
Solar Energy System - Detailed Report
Generated on: ${new Date().toLocaleDateString()}

=== SYSTEM OVERVIEW ===
Current Power Output: ${data.currentPower || '3.2 kW'}
Daily Energy Production: ${data.dailyEnergy || '28.4 kWh'}
Monthly Production: ${data.monthlyEnergy || '847.3 kWh'}
System Efficiency: ${data.efficiency || '94%'}

=== FINANCIAL METRICS ===
Monthly Savings: ${data.monthlySavings || '$284'}
Total Savings: ${data.totalSavings || '$3,420'}
Payback Progress: ${data.paybackProgress || '32%'}

=== ENVIRONMENTAL IMPACT ===
CO₂ Reduction: ${data.co2Saved || '2.4 tons'}
Trees Planted Equivalent: ${data.treesEquivalent || '32 trees'}
Water Conservation: ${data.waterSaved || '1,240 gallons'}

=== SYSTEM HEALTH ===
Solar Panels: ${data.panelStatus || 'Optimal'}
Inverter: ${data.inverterStatus || 'Optimal'}
Battery Storage: ${data.batteryHealth || '97%'}
Connection Status: ${data.connectionStatus || 'Connected'}

=== WEEKLY PERFORMANCE ===
Monday: 32.4 kWh ($18.50 saved)
Tuesday: 28.7 kWh ($16.20 saved)
Wednesday: 35.2 kWh ($20.10 saved)
Thursday: 31.8 kWh ($18.00 saved)
Friday: 29.6 kWh ($16.80 saved)
Saturday: 33.1 kWh ($19.20 saved)
Sunday: 30.9 kWh ($17.60 saved)

Total Weekly: 221.7 kWh ($126.40 saved)
    `;

    // Create and download the report
    const blob = new Blob([reportContent], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `solar-report-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    return { success: true, message: 'Report exported successfully!' };
  } catch (error) {
    console.error('Export failed:', error);
    return { success: false, message: 'Export failed. Please try again.' };
  }
};

export const exportToCSV = (data: any) => {
  try {
    // Create CSV content with comprehensive data
    const csvContent = [
      ['Metric', 'Value', 'Unit', 'Date'],
      ['Current Power Output', data.currentPower || '3.2', 'kW', new Date().toISOString()],
      ['Daily Energy Production', data.dailyEnergy || '28.4', 'kWh', new Date().toISOString()],
      ['Monthly Production', data.monthlyEnergy || '847.3', 'kWh', new Date().toISOString()],
      ['System Efficiency', data.efficiency || '94', '%', new Date().toISOString()],
      ['Monthly Savings', data.monthlySavings || '284', 'USD', new Date().toISOString()],
      ['Total Savings', data.totalSavings || '3420', 'USD', new Date().toISOString()],
      ['CO2 Reduction', data.co2Saved || '2.4', 'tons', new Date().toISOString()],
      ['Trees Equivalent', data.treesEquivalent || '32', 'trees', new Date().toISOString()],
      ['Water Conservation', data.waterSaved || '1240', 'gallons', new Date().toISOString()],
      ['Battery Health', data.batteryHealth || '97', '%', new Date().toISOString()],
      ['Payback Progress', data.paybackProgress || '32', '%', new Date().toISOString()],
      // Weekly data
      ['Monday Production', '32.4', 'kWh', new Date().toISOString()],
      ['Tuesday Production', '28.7', 'kWh', new Date().toISOString()],
      ['Wednesday Production', '35.2', 'kWh', new Date().toISOString()],
      ['Thursday Production', '31.8', 'kWh', new Date().toISOString()],
      ['Friday Production', '29.6', 'kWh', new Date().toISOString()],
      ['Saturday Production', '33.1', 'kWh', new Date().toISOString()],
      ['Sunday Production', '30.9', 'kWh', new Date().toISOString()],
    ]
      .map(row => row.join(','))
      .join('\n');

    // Create and download the CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `solar-data-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    return { success: true, message: 'Data exported successfully!' };
  } catch (error) {
    console.error('CSV export failed:', error);
    return { success: false, message: 'CSV export failed. Please try again.' };
  }
};