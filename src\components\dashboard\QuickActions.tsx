import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export const QuickActions = () => {
  const actions = [
    { icon: FileText, label: "View Reports", variant: "outline" as const },
    { icon: Wrench, label: "Maintenance", variant: "secondary" as const },
    { icon: Brain, label: "AI Tips", variant: "solar" as const },
  ];

  return (
    <div className="space-y-4">
      <h3 className="font-heading font-semibold text-foreground">Quick Actions</h3>
      
      <div className="grid grid-cols-1 gap-3">
        {actions.map((action, index) => {
          const Icon = action.icon;
          return (
            <Button
              key={index}
              variant={action.variant}
              className="justify-start h-auto p-4 text-left"
              onClick={() => {
                console.log(`${action.label} clicked`);
                // Add your action logic here
              }}
            >
              <div className="p-2 rounded-lg mr-3 bg-current/10">
                <Icon className="w-4 h-4" />
              </div>
              <span className="font-medium">{action.label}</span>
            </Button>
          );
        })}
      </div>
    </div>
  );
};