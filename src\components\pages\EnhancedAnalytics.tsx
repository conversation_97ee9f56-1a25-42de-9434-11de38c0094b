import { useState } from "react";
import { BarChart3, TrendingUp, Download, Filter, Calendar, Share2, Target, Zap } from "lucide-react";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { EnergyChart } from "../charts/EnergyChart";
import { useRealTimeData } from "@/hooks/useRealTimeData";
import { useToast } from "@/hooks/use-toast";
import { ProfessionalButton } from "@/components/ui/professional-button";

export const Analytics = () => {
  const { data } = useRealTimeData();
  const { toast } = useToast();
  const [selectedPeriod, setSelectedPeriod] = useState("week");
  const [selectedMetric, setSelectedMetric] = useState("generation");

  // Enhanced analytics data
  const performanceMetrics = {
    daily: {
      generation: [4.2, 5.8, 3.1, 6.2, 4.9, 7.1, 5.3],
      consumption: [3.8, 4.2, 2.9, 4.5, 3.7, 5.2, 4.1],
      efficiency: [94, 96, 89, 97, 93, 98, 95],
    },
    weekly: {
      generation: [35.2, 42.1, 38.7, 45.3, 41.2, 47.8, 44.1],
      consumption: [28.5, 31.2, 26.8, 33.1, 29.7, 35.4, 32.3],
      efficiency: [94.2, 95.8, 92.1, 96.7, 94.5, 97.2, 95.3],
    },
    monthly: {
      generation: [142, 168, 155, 181, 164, 191, 176],
      consumption: [114, 125, 107, 132, 119, 142, 129],
      efficiency: [94.1, 95.3, 91.8, 96.2, 93.9, 96.8, 95.1],
    },
  };

  const insights = [
    {
      title: "Peak Performance Day",
      value: "Yesterday",
      description: "Highest generation this month: 7.1 kWh",
      trend: "+23%",
      color: "text-green-500",
    },
    {
      title: "Efficiency Trend",
      value: "↗ Improving",
      description: "5-day average: 95.2% (+2.1%)",
      trend: "+2.1%",
      color: "text-blue-500",
    },
    {
      title: "Monthly Progress",
      value: "78%",
      description: "On track to exceed target by 12%",
      trend: "+12%",
      color: "text-purple-500",
    },
    {
      title: "Cost Savings",
      value: "$127",
      description: "This month vs. grid electricity",
      trend: "+$23",
      color: "text-green-500",
    },
  ];

  const forecasts = [
    { period: "Tomorrow", generation: "6.8 kWh", confidence: 95, weather: "Sunny" },
    { period: "This Week", generation: "45.2 kWh", confidence: 87, weather: "Mostly Sunny" },
    { period: "Next Week", generation: "38.7 kWh", confidence: 72, weather: "Mixed" },
  ];

  const handleExport = (type: string) => {
    toast({
      title: `${type} Export Started`,
      description: `Your ${selectedPeriod} analytics report is being generated...`,
    });
  };

  const handleShare = () => {
    toast({
      title: "Report Shared",
      description: "Analytics summary has been shared via email.",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-bg">
      <div className="p-4 pb-24 space-y-6 max-w-md mx-auto">
        {/* Header with Controls */}
        <Card className="solar-card p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <BarChart3 className="w-6 h-6 text-primary" />
              <h1 className="font-heading font-bold text-xl">Advanced Analytics</h1>
            </div>
            <Badge variant="secondary">Live Data</Badge>
          </div>

          <div className="grid grid-cols-2 gap-3 mb-4">
            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">Daily</SelectItem>
                <SelectItem value="week">Weekly</SelectItem>
                <SelectItem value="month">Monthly</SelectItem>
                <SelectItem value="year">Yearly</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedMetric} onValueChange={setSelectedMetric}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="generation">Generation</SelectItem>
                <SelectItem value="consumption">Consumption</SelectItem>
                <SelectItem value="efficiency">Efficiency</SelectItem>
                <SelectItem value="savings">Savings</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex gap-2">
            <ProfessionalButton variant="outline" size="sm" onClick={() => handleExport("PDF")}>
              <Download className="w-4 h-4" />
              PDF
            </ProfessionalButton>
            <ProfessionalButton variant="outline" size="sm" onClick={() => handleExport("CSV")}>
              <Download className="w-4 h-4" />
              CSV
            </ProfessionalButton>
            <ProfessionalButton variant="outline" size="sm" onClick={handleShare}>
              <Share2 className="w-4 h-4" />
              Share
            </ProfessionalButton>
          </div>
        </Card>

        {/* Key Performance Insights */}
        <Card className="solar-card p-6">
          <h3 className="font-heading font-semibold mb-4 flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-primary" />
            Performance Insights
          </h3>
          
          <div className="grid grid-cols-2 gap-3">
            {insights.map((insight, index) => (
              <div key={index} className="p-3 bg-muted/30 rounded-lg">
                <div className="text-xs text-muted-foreground font-medium mb-1">
                  {insight.title}
                </div>
                <div className="font-bold text-lg mb-1">{insight.value}</div>
                <div className="text-xs text-muted-foreground mb-2">
                  {insight.description}
                </div>
                <div className={`text-xs font-medium ${insight.color}`}>
                  {insight.trend}
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Interactive Charts */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="generation">Generation</TabsTrigger>
            <TabsTrigger value="efficiency">Efficiency</TabsTrigger>
            <TabsTrigger value="forecast">Forecast</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <Card className="solar-card p-6">
              <h3 className="font-heading font-semibold mb-4">Energy Overview</h3>
              <EnergyChart />
              <div className="mt-4 grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="font-mono font-bold text-primary">
                    {data.dailyGeneration.toFixed(1)} kWh
                  </div>
                  <div className="text-xs text-muted-foreground">Today</div>
                </div>
                <div>
                  <div className="font-mono font-bold text-secondary">
                    {data.weeklyGeneration.toFixed(0)} kWh
                  </div>
                  <div className="text-xs text-muted-foreground">This Week</div>
                </div>
                <div>
                  <div className="font-mono font-bold text-accent">
                    {data.monthlyGeneration.toFixed(0)} kWh
                  </div>
                  <div className="text-xs text-muted-foreground">This Month</div>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="generation">
            <Card className="solar-card p-6">
              <h3 className="font-heading font-semibold mb-4">Generation Analysis</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Peak Generation</span>
                  <span className="font-mono font-bold text-primary">
                    {data.peakPower.toFixed(1)} kW
                  </span>
                </div>
                <Progress value={85} className="h-2" />
                
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Average Generation</span>
                  <span className="font-mono font-bold text-secondary">
                    {data.averagePower.toFixed(1)} kW
                  </span>
                </div>
                <Progress value={65} className="h-2" />
                
                <div className="grid grid-cols-2 gap-4 mt-4">
                  <div className="text-center p-3 bg-muted/30 rounded-lg">
                    <div className="font-mono font-bold text-lg">342%</div>
                    <div className="text-xs text-muted-foreground">vs Last Month</div>
                  </div>
                  <div className="text-center p-3 bg-muted/30 rounded-lg">
                    <div className="font-mono font-bold text-lg">12.5 h</div>
                    <div className="text-xs text-muted-foreground">Peak Hours</div>
                  </div>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="efficiency">
            <Card className="solar-card p-6">
              <h3 className="font-heading font-semibold mb-4">System Efficiency</h3>
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-4xl font-mono font-bold text-green-500 mb-2">
                    {data.efficiency.toFixed(1)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Current Efficiency</div>
                </div>
                
                <Progress value={data.efficiency} className="h-3" />
                
                <div className="grid grid-cols-3 gap-3 text-center">
                  <div className="p-3 bg-green-500/10 rounded-lg">
                    <div className="font-mono font-bold text-green-500">97.2%</div>
                    <div className="text-xs text-muted-foreground">Best</div>
                  </div>
                  <div className="p-3 bg-yellow-500/10 rounded-lg">
                    <div className="font-mono font-bold text-yellow-500">95.1%</div>
                    <div className="text-xs text-muted-foreground">Average</div>
                  </div>
                  <div className="p-3 bg-red-500/10 rounded-lg">
                    <div className="font-mono font-bold text-red-500">89.3%</div>
                    <div className="text-xs text-muted-foreground">Lowest</div>
                  </div>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="forecast">
            <Card className="solar-card p-6">
              <h3 className="font-heading font-semibold mb-4 flex items-center gap-2">
                <Target className="w-5 h-5" />
                AI Forecasting
              </h3>
              
              <div className="space-y-3">
                {forecasts.map((forecast, index) => (
                  <div key={index} className="p-4 bg-muted/30 rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <div className="font-medium">{forecast.period}</div>
                        <div className="text-xs text-muted-foreground">
                          {forecast.weather} conditions
                        </div>
                      </div>
                      <Badge variant="outline">
                        {forecast.confidence}% confidence
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="font-mono font-bold text-primary">
                        {forecast.generation}
                      </span>
                      <Progress value={forecast.confidence} className="w-20 h-2" />
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-4 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                <div className="flex items-center gap-2 text-blue-600 mb-2">
                  <Zap className="w-4 h-4" />
                  <span className="font-medium text-sm">Smart Recommendation</span>
                </div>
                <p className="text-xs text-blue-600">
                  Based on weather patterns, consider running energy-intensive appliances between 11 AM - 2 PM for optimal solar utilization.
                </p>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};