import { useState } from "react";
import { Lightbulb, Star, CheckCircle, Clock, Target, TrendingUp, Leaf, DollarSign, Zap, Shield, Bot } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useRealTimeData } from "@/hooks/useRealTimeData";
import { useToast } from "@/hooks/use-toast";
import { ProfessionalButton } from "@/components/ui/professional-button";

export const Recommendations = () => {
  const { data, addAlert } = useRealTimeData();
  const { toast } = useToast();
  const [implementedTips, setImplementedTips] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState("all");

  const aiRecommendations = {
    urgent: [
      {
        id: "urgent1",
        title: "Panel Cleaning Required",
        description: "Efficiency dropped 8% - dust accumulation detected",
        impact: "High",
        savings: "$45/month",
        effort: "Low",
        category: "maintenance",
        priority: 1,
        steps: ["Schedule professional cleaning", "Check inverter logs", "Monitor next 48 hours"],
        timeframe: "This week",
      },
      {
        id: "urgent2", 
        title: "Peak Hour Optimization",
        description: "Shift EV charging to 11 AM-2 PM for maximum solar usage",
        impact: "Medium",
        savings: "$32/month",
        effort: "Low",
        category: "usage",
        priority: 2,
        steps: ["Update charging schedule", "Enable smart charging", "Monitor consumption"],
        timeframe: "Today",
      },
    ],
    performance: [
      {
        id: "perf1",
        title: "Add Battery Storage",
        description: "Store excess energy for evening use - ROI in 4.2 years",
        impact: "Very High",
        savings: "$156/month",
        effort: "High",
        category: "upgrade",
        priority: 3,
        roi: "4.2 years",
        steps: ["Get quotes", "Check rebates", "Install planning"],
        timeframe: "Next quarter",
      },
      {
        id: "perf2",
        title: "Smart Thermostat Integration",
        description: "Optimize HVAC based on solar production patterns",
        impact: "Medium",
        savings: "$28/month",
        effort: "Medium",
        category: "automation",
        priority: 4,
        steps: ["Choose smart thermostat", "Professional installation", "Configure schedules"],
        timeframe: "Next month",
      },
    ],
    financial: [
      {
        id: "fin1",
        title: "Net Metering Upgrade",
        description: "Switch to time-of-use billing for 15% more credits",
        impact: "High",
        savings: "$67/month",
        effort: "Low",
        category: "billing",
        priority: 5,
        steps: ["Contact utility", "Submit application", "Update meter"],
        timeframe: "This month",
      },
      {
        id: "fin2",
        title: "Solar Tax Credit Filing",
        description: "Claim additional 30% federal tax credit for recent upgrades",
        impact: "High",
        savings: "$2,400/year",
        effort: "Low",
        category: "incentives",
        priority: 6,
        steps: ["Gather receipts", "Complete IRS Form 5695", "File with tax return"],
        timeframe: "Tax season",
      },
    ],
  };

  const personalizedInsights = [
    {
      title: "Your Energy Profile",
      insight: "You're a morning energy user - 60% consumption before noon",
      recommendation: "Consider shifting dishwasher and laundry to 10 AM-1 PM",
      potential: "$23/month",
    },
    {
      title: "Weather Pattern Analysis",
      insight: "Cloudy afternoons reduce your generation by 40%",
      recommendation: "Use battery backup during 3-6 PM on cloudy days",
      potential: "$31/month",
    },
    {
      title: "Seasonal Optimization",
      insight: "Winter production is 35% lower than optimal",
      recommendation: "Adjust panel tilt or add seasonal tracking",
      potential: "$89/month in winter",
    },
  ];

  const implementTip = (tipId: string, tipTitle: string) => {
    setImplementedTips(prev => [...prev, tipId]);
    addAlert({
      title: "Recommendation Implemented",
      message: `You've started implementing: ${tipTitle}`,
      type: "success",
      read: false,
    });
    toast({
      title: "Tip Implemented!",
      description: `Great job implementing: ${tipTitle}`,
    });
  };

  const getAllRecommendations = () => {
    return [...aiRecommendations.urgent, ...aiRecommendations.performance, ...aiRecommendations.financial];
  };

  const getFilteredRecommendations = () => {
    const all = getAllRecommendations();
    if (selectedCategory === "all") return all;
    return all.filter(rec => rec.category === selectedCategory);
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case "Very High": return "text-red-500";
      case "High": return "text-orange-500";
      case "Medium": return "text-yellow-500";
      default: return "text-green-500";
    }
  };

  const getEffortColor = (effort: string) => {
    switch (effort) {
      case "Low": return "bg-green-500/10 text-green-600";
      case "Medium": return "bg-yellow-500/10 text-yellow-600";
      default: return "bg-red-500/10 text-red-600";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-bg">
      <div className="p-4 pb-24 space-y-6 max-w-md mx-auto">
        {/* Header */}
        <Card className="solar-card p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Bot className="w-6 h-6 text-primary" />
              <h1 className="font-heading font-bold text-xl">AI Recommendations</h1>
            </div>
            <Badge variant="secondary" className="animate-pulse">Live AI</Badge>
          </div>
          
          <div className="text-center p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg border border-blue-500/20">
            <div className="text-2xl font-mono font-bold text-primary mb-2">
              ${getAllRecommendations().reduce((sum, rec) => sum + parseInt(rec.savings.replace(/\D/g, '')), 0)}
            </div>
            <div className="text-sm text-muted-foreground">
              Total Monthly Savings Potential
            </div>
          </div>
        </Card>

        {/* Personalized AI Insights */}
        <Card className="solar-card p-6">
          <h3 className="font-heading font-semibold mb-4 flex items-center gap-2">
            <Bot className="w-5 h-5 text-primary" />
            AI Personal Insights
          </h3>
          
          <div className="space-y-4">
            {personalizedInsights.map((insight, index) => (
              <div key={index} className="p-4 bg-muted/30 rounded-lg">
                <div className="font-medium mb-2">{insight.title}</div>
                <div className="text-sm text-muted-foreground mb-2">{insight.insight}</div>
                <div className="text-sm font-medium text-primary mb-1">{insight.recommendation}</div>
                <Badge variant="outline" className="text-xs">
                  Potential: {insight.potential}
                </Badge>
              </div>
            ))}
          </div>
        </Card>

        {/* Category Filter */}
        <Card className="solar-card p-4">
          <div className="grid grid-cols-4 gap-2">
            {["all", "maintenance", "usage", "upgrade", "automation"].map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="text-xs capitalize"
              >
                {category}
              </Button>
            ))}
          </div>
        </Card>

        {/* Recommendations by Priority */}
        <Tabs defaultValue="urgent" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="urgent">Urgent ({aiRecommendations.urgent.length})</TabsTrigger>
            <TabsTrigger value="performance">Performance ({aiRecommendations.performance.length})</TabsTrigger>
            <TabsTrigger value="financial">Financial ({aiRecommendations.financial.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="urgent" className="space-y-4">
            {aiRecommendations.urgent.map((rec) => (
              <Card key={rec.id} className="solar-card p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <Lightbulb className="w-5 h-5 text-yellow-500" />
                    <div>
                      <h4 className="font-semibold">{rec.title}</h4>
                      <p className="text-sm text-muted-foreground">{rec.description}</p>
                    </div>
                  </div>
                  <Badge variant="destructive">Urgent</Badge>
                </div>

                <div className="grid grid-cols-3 gap-3 mb-4">
                  <div className="text-center p-2 bg-muted/30 rounded">
                    <div className={`font-bold ${getImpactColor(rec.impact)}`}>{rec.impact}</div>
                    <div className="text-xs text-muted-foreground">Impact</div>
                  </div>
                  <div className="text-center p-2 bg-muted/30 rounded">
                    <div className="font-bold text-green-500">{rec.savings}</div>
                    <div className="text-xs text-muted-foreground">Savings</div>
                  </div>
                  <div className="text-center p-2 bg-muted/30 rounded">
                    <Badge className={getEffortColor(rec.effort)} variant="outline">
                      {rec.effort}
                    </Badge>
                    <div className="text-xs text-muted-foreground mt-1">Effort</div>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="font-medium text-sm">Action Steps:</div>
                  {rec.steps.map((step, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <div className="w-5 h-5 rounded-full bg-primary/20 text-primary flex items-center justify-center text-xs font-bold">
                        {index + 1}
                      </div>
                      <span>{step}</span>
                    </div>
                  ))}
                </div>

                <div className="flex gap-2">
                  <ProfessionalButton
                    onClick={() => implementTip(rec.id, rec.title)}
                    disabled={implementedTips.includes(rec.id)}
                    className="flex-1"
                  >
                    {implementedTips.includes(rec.id) ? (
                      <>
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Implemented
                      </>
                    ) : (
                      <>
                        <Target className="w-4 h-4 mr-2" />
                        Start Implementation
                      </>
                    )}
                  </ProfessionalButton>
                </div>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="performance" className="space-y-4">
            {aiRecommendations.performance.map((rec) => (
              <Card key={rec.id} className="solar-card p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5 text-blue-500" />
                    <div>
                      <h4 className="font-semibold">{rec.title}</h4>
                      <p className="text-sm text-muted-foreground">{rec.description}</p>
                    </div>
                  </div>
                  <Badge variant="secondary">Performance</Badge>
                </div>

                <div className="grid grid-cols-3 gap-3 mb-4">
                  <div className="text-center p-2 bg-muted/30 rounded">
                    <div className={`font-bold ${getImpactColor(rec.impact)}`}>{rec.impact}</div>
                    <div className="text-xs text-muted-foreground">Impact</div>
                  </div>
                  <div className="text-center p-2 bg-muted/30 rounded">
                    <div className="font-bold text-green-500">{rec.savings}</div>
                    <div className="text-xs text-muted-foreground">Savings</div>
                  </div>
                  <div className="text-center p-2 bg-muted/30 rounded">
                    <div className="font-bold text-blue-500">{rec.roi || rec.timeframe}</div>
                    <div className="text-xs text-muted-foreground">{rec.roi ? "ROI" : "Timeline"}</div>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="font-medium text-sm">Implementation Plan:</div>
                  {rec.steps.map((step, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <div className="w-5 h-5 rounded-full bg-blue-500/20 text-blue-500 flex items-center justify-center text-xs font-bold">
                        {index + 1}
                      </div>
                      <span>{step}</span>
                    </div>
                  ))}
                </div>

                <div className="flex gap-2">
                  <ProfessionalButton
                    onClick={() => implementTip(rec.id, rec.title)}
                    disabled={implementedTips.includes(rec.id)}
                    variant="outline"
                    className="flex-1"
                  >
                    {implementedTips.includes(rec.id) ? (
                      <>
                        <CheckCircle className="w-4 h-4 mr-2" />
                        In Progress
                      </>
                    ) : (
                      <>
                        <Clock className="w-4 h-4 mr-2" />
                        Plan Implementation
                      </>
                    )}
                  </ProfessionalButton>
                </div>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="financial" className="space-y-4">
            {aiRecommendations.financial.map((rec) => (
              <Card key={rec.id} className="solar-card p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-5 h-5 text-green-500" />
                    <div>
                      <h4 className="font-semibold">{rec.title}</h4>
                      <p className="text-sm text-muted-foreground">{rec.description}</p>
                    </div>
                  </div>
                  <Badge variant="outline" className="text-green-600 border-green-600">Financial</Badge>
                </div>

                <div className="grid grid-cols-2 gap-3 mb-4">
                  <div className="text-center p-3 bg-green-500/10 rounded border border-green-500/20">
                    <div className="font-bold text-green-500 text-lg">{rec.savings}</div>
                    <div className="text-xs text-muted-foreground">Potential Savings</div>
                  </div>
                  <div className="text-center p-3 bg-muted/30 rounded">
                    <div className="font-bold">{rec.timeframe}</div>
                    <div className="text-xs text-muted-foreground">Timeframe</div>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="font-medium text-sm">Action Items:</div>
                  {rec.steps.map((step, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <div className="w-5 h-5 rounded-full bg-green-500/20 text-green-500 flex items-center justify-center text-xs font-bold">
                        {index + 1}
                      </div>
                      <span>{step}</span>
                    </div>
                  ))}
                </div>

                <div className="flex gap-2">
                  <ProfessionalButton
                    onClick={() => implementTip(rec.id, rec.title)}
                    disabled={implementedTips.includes(rec.id)}
                    className="flex-1 bg-green-500 hover:bg-green-600"
                  >
                    {implementedTips.includes(rec.id) ? (
                      <>
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Completed
                      </>
                    ) : (
                      <>
                        <DollarSign className="w-4 h-4 mr-2" />
                        Start Saving
                      </>
                    )}
                  </ProfessionalButton>
                </div>
              </Card>
            ))}
          </TabsContent>
        </Tabs>

        {/* Progress Tracking */}
        <Card className="solar-card p-6">
          <h3 className="font-heading font-semibold mb-4 flex items-center gap-2">
            <Star className="w-5 h-5 text-yellow-500" />
            Implementation Progress
          </h3>
          
          <div className="space-y-4">
            <div className="flex justify-between text-sm">
              <span>Recommendations Implemented</span>
              <span className="font-mono">{implementedTips.length} / {getAllRecommendations().length}</span>
            </div>
            <Progress value={(implementedTips.length / getAllRecommendations().length) * 100} className="h-2" />
            
            <div className="grid grid-cols-2 gap-3 mt-4">
              <div className="text-center p-3 bg-green-500/10 rounded border border-green-500/20">
                <div className="font-bold text-green-500">
                  ${implementedTips.length * 35}
                </div>
                <div className="text-xs text-muted-foreground">Est. Monthly Savings</div>
              </div>
              <div className="text-center p-3 bg-blue-500/10 rounded border border-blue-500/20">
                <div className="font-bold text-blue-500">
                  {Math.round((implementedTips.length / getAllRecommendations().length) * 100)}%
                </div>
                <div className="text-xs text-muted-foreground">Optimization Score</div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};