import { Activity, Shield, AlertTriangle, CheckCircle, Settings } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export const SystemHealthCard = () => {
  const systemHealth = 98;
  const lastMaintenance = "15 days ago";
  const nextMaintenance = "75 days";
  const activePanels = 24;
  const totalPanels = 24;
  const inverterStatus = "optimal";
  const batteryHealth = 96;

  const healthColor = systemHealth >= 95 ? "success" : systemHealth >= 85 ? "warning" : "danger";
  const healthText = systemHealth >= 95 ? "Excellent" : systemHealth >= 85 ? "Good" : "Needs Attention";

  return (
    <Card className="solar-card p-6 relative overflow-hidden">
      <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-success/10 to-transparent rounded-full -translate-y-4 translate-x-4"></div>
      
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-success/20 rounded-xl shadow-success">
            <Shield className="w-6 h-6 text-success" />
          </div>
          <div>
            <h3 className="font-heading font-bold text-card-foreground">System Health</h3>
            <p className="text-sm text-muted-foreground">Performance monitoring</p>
          </div>
        </div>
        <Settings className="w-5 h-5 text-muted-foreground" />
      </div>
      
      <div className="space-y-6">
        {/* Overall Health Score */}
        <div className="text-center bg-success/5 rounded-xl p-4">
          <div className="text-3xl font-mono font-bold text-success mb-1">
            {systemHealth}%
          </div>
          <div className="text-sm text-muted-foreground">Overall system health</div>
          <Badge variant="outline" className={`mt-2 status-${healthColor}`}>
            <CheckCircle className="w-3 h-3 mr-1" />
            {healthText}
          </Badge>
        </div>

        {/* Component Status */}
        <div className="space-y-4">
          <h4 className="font-medium text-card-foreground">Component Status</h4>
          
          <div className="space-y-3">
            {/* Solar Panels */}
            <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">Solar Panels</span>
              </div>
              <div className="text-right">
                <div className="text-sm font-bold text-success">{activePanels}/{totalPanels}</div>
                <div className="text-xs text-muted-foreground">Active</div>
              </div>
            </div>

            {/* Inverter */}
            <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">Inverter</span>
              </div>
              <div className="text-right">
                <div className="text-sm font-bold text-success capitalize">{inverterStatus}</div>
                <div className="text-xs text-muted-foreground">Status</div>
              </div>
            </div>

            {/* Battery System */}
            <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">Battery System</span>
              </div>
              <div className="text-right">
                <div className="text-sm font-bold text-success">{batteryHealth}%</div>
                <div className="text-xs text-muted-foreground">Health</div>
              </div>
            </div>
          </div>
        </div>

        {/* Maintenance Info */}
        <div className="pt-2 border-t border-border/50">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-sm font-bold text-muted-foreground">{lastMaintenance}</div>
              <div className="text-xs text-muted-foreground">Last service</div>
            </div>
            <div>
              <div className="text-sm font-bold text-accent">{nextMaintenance}</div>
              <div className="text-xs text-muted-foreground">Next service</div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex gap-2">
          <button className="flex-1 btn-eco text-xs py-2">
            <Activity className="w-3 h-3 mr-1" />
            View Details
          </button>
          <button className="flex-1 bg-muted/50 text-muted-foreground text-xs py-2 px-4 rounded-lg hover:bg-muted transition-colors">
            Schedule Service
          </button>
        </div>
      </div>
    </Card>
  );
};