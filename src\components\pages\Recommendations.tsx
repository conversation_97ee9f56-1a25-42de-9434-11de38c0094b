import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Brain, Sun, Droplets, Zap, MessageCircle } from "lucide-react";

export const Recommendations = () => {
  const recommendations = [
    {
      icon: Sun,
      title: "Peak Sunlight Hours",
      description: "Best generation expected between 11 AM - 3 PM today",
      type: "timing",
      color: "text-secondary"
    },
    {
      icon: Droplets,
      title: "Cleaning Reminder",
      description: "Consider cleaning panels - efficiency down 5%",
      type: "maintenance",
      color: "text-blue-500"
    },
    {
      icon: Zap,
      title: "Energy Optimization",
      description: "Run heavy appliances now for maximum solar usage",
      type: "usage",
      color: "text-accent"
    }
  ];

  return (
    <div className="p-4 pb-24 space-y-6 max-w-md mx-auto">
      <div className="text-center">
        <h2 className="text-2xl font-heading font-bold text-foreground">AI Recommendations</h2>
        <p className="text-muted-foreground">Smart tips for optimal performance</p>
      </div>

      <div className="space-y-4">
        {recommendations.map((rec, index) => {
          const Icon = rec.icon;
          return (
            <Card key={index} className="solar-card p-6">
              <div className="flex items-start gap-4">
                <div className={`p-3 bg-primary/10 rounded-xl ${rec.color}`}>
                  <Icon className="w-5 h-5" />
                </div>
                <div className="flex-1">
                  <h3 className="font-heading font-semibold text-card-foreground mb-1">
                    {rec.title}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {rec.description}
                  </p>
                </div>
              </div>
            </Card>
          );
        })}
      </div>

      <Card className="solar-card p-6 bg-gradient-to-br from-primary/5 to-secondary/5">
        <div className="flex items-center gap-3 mb-4">
          <Brain className="w-6 h-6 text-primary" />
          <h3 className="font-heading font-semibold text-foreground">AI Assistant</h3>
        </div>
        <p className="text-sm text-muted-foreground mb-4">
          Get personalized solar advice and troubleshooting help
        </p>
        <Button className="w-full">
          <MessageCircle className="w-4 h-4 mr-2" />
          Ask AI
        </Button>
      </Card>
    </div>
  );
};