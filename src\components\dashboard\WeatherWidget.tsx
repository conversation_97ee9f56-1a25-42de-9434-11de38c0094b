import { Cloud, Sun, CloudRain } from "lucide-react";
import { Card } from "@/components/ui/card";

export const WeatherWidget = () => {
  const weather = {
    temp: 24,
    condition: "sunny",
    sunlight: 85,
    cloudCover: 15
  };

  const getWeatherIcon = () => {
    switch (weather.condition) {
      case "sunny":
        return <Sun className="w-6 h-6 text-secondary" />;
      case "cloudy":
        return <Cloud className="w-6 h-6 text-muted-foreground" />;
      case "rainy":
        return <CloudRain className="w-6 h-6 text-blue-500" />;
      default:
        return <Sun className="w-6 h-6 text-secondary" />;
    }
  };

  return (
    <Card className="solar-card p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-heading font-semibold text-card-foreground">Weather</h3>
        {getWeatherIcon()}
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <div className="text-2xl font-mono font-bold text-foreground">
            {weather.temp}°C
          </div>
          <div className="text-sm text-muted-foreground">Temperature</div>
        </div>
        
        <div>
          <div className="text-2xl font-mono font-bold text-primary">
            {weather.sunlight}%
          </div>
          <div className="text-sm text-muted-foreground">Sunlight</div>
        </div>
      </div>
    </Card>
  );
};