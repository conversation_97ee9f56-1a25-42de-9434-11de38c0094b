import { Leaf, TreePine, Droplets, Award } from "lucide-react";
import { Card } from "@/components/ui/card";

export const EnvironmentalImpactCard = () => {
  const co2Saved = 2.4; // tons this year
  const treesEquivalent = 58;
  const waterSaved = 1240; // gallons
  const coalAvoided = 2800; // pounds

  return (
    <Card className="eco-card p-6 relative overflow-hidden">
      <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-eco opacity-15 rounded-full -translate-y-4 translate-x-4"></div>
      
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-secondary/20 rounded-xl shadow-success">
            <Leaf className="w-6 h-6 text-secondary" />
          </div>
          <div>
            <h3 className="font-heading font-bold text-card-foreground">Environmental Impact</h3>
            <p className="text-sm text-muted-foreground">Your contribution to Earth</p>
          </div>
        </div>
        <Award className="w-5 h-5 text-secondary" />
      </div>
      
      <div className="space-y-6">
        {/* CO2 Reduction */}
        <div className="text-center bg-secondary/5 rounded-xl p-4">
          <div className="text-3xl font-mono font-bold text-secondary mb-1">
            {co2Saved} <span className="text-lg text-muted-foreground">tons</span>
          </div>
          <div className="text-sm text-muted-foreground">CO₂ emissions avoided this year</div>
        </div>

        {/* Environmental Metrics Grid */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-accent/5 rounded-xl p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <TreePine className="w-5 h-5 text-accent" />
            </div>
            <div className="text-xl font-bold text-accent">{treesEquivalent}</div>
            <div className="text-xs text-muted-foreground">Trees planted equivalent</div>
          </div>
          
          <div className="bg-primary/5 rounded-xl p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <Droplets className="w-5 h-5 text-primary" />
            </div>
            <div className="text-xl font-bold text-primary">{waterSaved.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">Gallons water saved</div>
          </div>
        </div>

        {/* Coal Equivalent */}
        <div className="bg-warning/5 rounded-xl p-4 text-center border border-warning/20">
          <div className="text-lg font-bold text-warning mb-1">
            {coalAvoided.toLocaleString()} lbs
          </div>
          <div className="text-sm text-muted-foreground">Coal burning avoided</div>
        </div>

        {/* Impact Summary */}
        <div className="pt-2 border-t border-border/50">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Your green score</span>
            <div className="flex items-center gap-2">
              <div className="w-16 h-2 bg-muted/50 rounded-full overflow-hidden">
                <div className="w-4/5 h-full bg-gradient-eco rounded-full"></div>
              </div>
              <span className="font-bold text-secondary">Excellent</span>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};