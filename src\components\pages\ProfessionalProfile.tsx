import { User, Settings, <PERSON>, Bell, Download, Star, Award } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Avatar } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { ProfessionalButton } from "@/components/ui/professional-button";

export const ProfessionalProfile = () => {
  const user = {
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/placeholder.svg",
    plan: "Premium Solar",
    joinDate: "March 2024",
    systemSize: "8.5 kW",
    totalSavings: 2450,
    co2Avoided: 3200,
    achievements: [
      { name: "Green Pioneer", description: "First month carbon negative", date: "Mar 2024" },
      { name: "Efficiency Expert", description: "Maintained 95%+ efficiency for 30 days", date: "Apr 2024" },
      { name: "Solar Champion", description: "Generated 1000+ kWh", date: "May 2024" },
    ],
    stats: {
      systemHealth: 97,
      monthlyGoal: 85,
      yearlyGoal: 73,
    }
  };

  return (
    <div className="min-h-screen bg-gradient-bg">
      <div className="p-4 pb-24 space-y-6 max-w-md mx-auto">
        {/* Profile Header */}
        <Card className="solar-card p-6 text-center">
          <div className="relative inline-block mb-4">
            <Avatar className="w-20 h-20 mx-auto border-4 border-primary/20">
              <img src={user.avatar} alt={user.name} className="w-full h-full object-cover" />
            </Avatar>
            <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
              <div className="w-2 h-2 bg-white rounded-full"></div>
            </div>
          </div>
          
          <h2 className="font-heading font-bold text-xl mb-1">{user.name}</h2>
          <p className="text-muted-foreground text-sm mb-3">{user.email}</p>
          
          <div className="flex items-center justify-center gap-2 mb-4">
            <Badge variant="default" className="bg-gradient-premium text-white">
              <Star className="w-3 h-3 mr-1" />
              {user.plan}
            </Badge>
            <Badge variant="outline">
              Since {user.joinDate}
            </Badge>
          </div>

          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="font-mono font-bold text-lg text-primary">{user.systemSize}</div>
              <div className="text-xs text-muted-foreground">System Size</div>
            </div>
            <div>
              <div className="font-mono font-bold text-lg text-green-500">${user.totalSavings}</div>
              <div className="text-xs text-muted-foreground">Total Savings</div>
            </div>
            <div>
              <div className="font-mono font-bold text-lg text-secondary">{user.co2Avoided}kg</div>
              <div className="text-xs text-muted-foreground">CO₂ Avoided</div>
            </div>
          </div>
        </Card>

        {/* Progress Goals */}
        <Card className="solar-card p-6">
          <h3 className="font-heading font-semibold mb-4 flex items-center gap-2">
            <Award className="w-5 h-5 text-primary" />
            Goals & Progress
          </h3>
          
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>System Health</span>
                <span className="font-mono">{user.stats.systemHealth}%</span>
              </div>
              <Progress value={user.stats.systemHealth} className="h-2" />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Monthly Goal</span>
                <span className="font-mono">{user.stats.monthlyGoal}%</span>
              </div>
              <Progress value={user.stats.monthlyGoal} className="h-2" />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Yearly Target</span>
                <span className="font-mono">{user.stats.yearlyGoal}%</span>
              </div>
              <Progress value={user.stats.yearlyGoal} className="h-2" />
            </div>
          </div>
        </Card>

        {/* Achievements */}
        <Card className="solar-card p-6">
          <h3 className="font-heading font-semibold mb-4 flex items-center gap-2">
            <Award className="w-5 h-5 text-primary" />
            Recent Achievements
          </h3>
          
          <div className="space-y-3">
            {user.achievements.map((achievement, index) => (
              <div key={index} className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                <div className="w-10 h-10 bg-gradient-premium rounded-lg flex items-center justify-center">
                  <Award className="w-5 h-5 text-white" />
                </div>
                <div className="flex-1">
                  <div className="font-medium text-sm">{achievement.name}</div>
                  <div className="text-xs text-muted-foreground">{achievement.description}</div>
                  <div className="text-xs text-primary font-medium">{achievement.date}</div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Quick Actions */}
        <Card className="solar-card p-6">
          <h3 className="font-heading font-semibold mb-4">Quick Actions</h3>
          
          <div className="grid grid-cols-2 gap-3">
            <ProfessionalButton variant="outline" size="sm" className="h-auto p-3 flex-col">
              <Settings className="w-4 h-4 mb-1" />
              <span className="text-xs">Settings</span>
            </ProfessionalButton>
            
            <ProfessionalButton variant="outline" size="sm" className="h-auto p-3 flex-col">
              <Bell className="w-4 h-4 mb-1" />
              <span className="text-xs">Notifications</span>
            </ProfessionalButton>
            
            <ProfessionalButton variant="outline" size="sm" className="h-auto p-3 flex-col">
              <Download className="w-4 h-4 mb-1" />
              <span className="text-xs">Reports</span>
            </ProfessionalButton>
            
            <ProfessionalButton variant="outline" size="sm" className="h-auto p-3 flex-col">
              <Shield className="w-4 h-4 mb-1" />
              <span className="text-xs">Security</span>
            </ProfessionalButton>
          </div>
        </Card>
      </div>
    </div>
  );
};