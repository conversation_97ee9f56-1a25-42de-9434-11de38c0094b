import { useState } from "react";
import { BarChart3, TrendingUp, Download, Filter, Calendar, Share2, Target, Zap, Activity, DollarSign } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { EnergyChart } from "../charts/EnergyChart";
import { useRealTimeData } from "@/hooks/useRealTimeData";
import { useToast } from "@/hooks/use-toast";
import { ProfessionalButton } from "@/components/ui/professional-button";

export const ComprehensiveAnalytics = () => {
  const { data } = useRealTimeData();
  const { toast } = useToast();
  const [selectedPeriod, setSelectedPeriod] = useState("week");
  const [selectedMetric, setSelectedMetric] = useState("generation");
  const [selectedComparison, setSelectedComparison] = useState("previousPeriod");

  // Comprehensive analytics data
  const detailedMetrics = {
    financial: {
      savings: { current: 1247, previous: 1089, target: 1500 },
      revenue: { current: 324, previous: 298, target: 400 },
      roi: { current: 12.5, previous: 11.8, target: 15 },
      payback: { years: 6.2, months: 74.4, percentage: 23 },
    },
    environmental: {
      co2Avoided: { current: 2.8, previous: 2.4, annual: 28.5 },
      treesEquivalent: { current: 127, previous: 109, annual: 1285 },
      coalAvoided: { current: 1234, previous: 1087, annual: 12450 },
    },
    technical: {
      degradation: { annual: 0.4, cumulative: 2.1, expected: 0.5 },
      availability: { current: 99.8, previous: 99.2, target: 99.5 },
      pr: { current: 82.5, previous: 81.2, target: 85 },
    },
    comparative: {
      neighbors: { average: 156, userValue: 187, percentile: 78 },
      region: { average: 142, userValue: 187, percentile: 85 },
      optimal: { theoretical: 210, actual: 187, efficiency: 89 },
    }
  };

  const handleExport = (type: string) => {
    toast({
      title: `${type} Export Started`,
      description: `Your comprehensive ${selectedPeriod} analytics report is being generated...`,
    });
  };

  const handleShare = () => {
    toast({
      title: "Report Shared",
      description: "Comprehensive analytics summary has been shared via email.",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-bg">
      <div className="p-4 pb-24 space-y-6 max-w-md mx-auto">
        {/* Enhanced Header with Controls */}
        <Card className="solar-card p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <BarChart3 className="w-6 h-6 text-primary" />
              <h1 className="font-heading font-bold text-xl">Comprehensive Analytics</h1>
            </div>
            <Badge variant="secondary" className="animate-pulse">Live Data</Badge>
          </div>

          <div className="grid grid-cols-1 gap-3 mb-4">
            <div className="grid grid-cols-2 gap-3">
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="day">Daily</SelectItem>
                  <SelectItem value="week">Weekly</SelectItem>
                  <SelectItem value="month">Monthly</SelectItem>
                  <SelectItem value="quarter">Quarterly</SelectItem>
                  <SelectItem value="year">Yearly</SelectItem>
                </SelectContent>
              </Select>

              <Select value={selectedMetric} onValueChange={setSelectedMetric}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="generation">Generation</SelectItem>
                  <SelectItem value="consumption">Consumption</SelectItem>
                  <SelectItem value="efficiency">Efficiency</SelectItem>
                  <SelectItem value="financial">Financial</SelectItem>
                  <SelectItem value="environmental">Environmental</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Select value={selectedComparison} onValueChange={setSelectedComparison}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="previousPeriod">Previous Period</SelectItem>
                <SelectItem value="sameLastYear">Same Period Last Year</SelectItem>
                <SelectItem value="neighbors">Neighborhood Average</SelectItem>
                <SelectItem value="optimal">Optimal Performance</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex gap-2">
            <ProfessionalButton variant="outline" size="sm" onClick={() => handleExport("PDF")}>
              <Download className="w-4 h-4" />
              PDF
            </ProfessionalButton>
            <ProfessionalButton variant="outline" size="sm" onClick={() => handleExport("Excel")}>
              <Download className="w-4 h-4" />
              Excel
            </ProfessionalButton>
            <ProfessionalButton variant="outline" size="sm" onClick={handleShare}>
              <Share2 className="w-4 h-4" />
              Share
            </ProfessionalButton>
          </div>
        </Card>

        {/* Financial Analytics */}
        <Card className="solar-card p-6">
          <h3 className="font-heading font-semibold mb-4 flex items-center gap-2">
            <DollarSign className="w-5 h-5 text-green-500" />
            Financial Performance
          </h3>
          
          <div className="grid grid-cols-2 gap-3 mb-4">
            <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20">
              <div className="text-xs text-muted-foreground font-medium mb-1">Monthly Savings</div>
              <div className="font-bold text-lg text-green-500">${detailedMetrics.financial.savings.current}</div>
              <div className="text-xs text-green-600">
                +${detailedMetrics.financial.savings.current - detailedMetrics.financial.savings.previous} vs last month
              </div>
            </div>
            
            <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
              <div className="text-xs text-muted-foreground font-medium mb-1">Energy Revenue</div>
              <div className="font-bold text-lg text-blue-500">${detailedMetrics.financial.revenue.current}</div>
              <div className="text-xs text-blue-600">
                +${detailedMetrics.financial.revenue.current - detailedMetrics.financial.revenue.previous} vs last month
              </div>
            </div>

            <div className="p-3 bg-purple-500/10 rounded-lg border border-purple-500/20">
              <div className="text-xs text-muted-foreground font-medium mb-1">ROI</div>
              <div className="font-bold text-lg text-purple-500">{detailedMetrics.financial.roi.current}%</div>
              <div className="text-xs text-purple-600">
                +{(detailedMetrics.financial.roi.current - detailedMetrics.financial.roi.previous).toFixed(1)}% improvement
              </div>
            </div>

            <div className="p-3 bg-orange-500/10 rounded-lg border border-orange-500/20">
              <div className="text-xs text-muted-foreground font-medium mb-1">Payback Progress</div>
              <div className="font-bold text-lg text-orange-500">{detailedMetrics.financial.payback.percentage}%</div>
              <div className="text-xs text-orange-600">
                {detailedMetrics.financial.payback.years} years remaining
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Savings Target Progress</span>
              <span className="font-mono">
                ${detailedMetrics.financial.savings.current} / ${detailedMetrics.financial.savings.target}
              </span>
            </div>
            <Progress 
              value={(detailedMetrics.financial.savings.current / detailedMetrics.financial.savings.target) * 100} 
              className="h-2" 
            />
          </div>
        </Card>

        {/* Environmental Impact */}
        <Card className="solar-card p-6">
          <h3 className="font-heading font-semibold mb-4 flex items-center gap-2">
            <Activity className="w-5 h-5 text-green-500" />
            Environmental Impact
          </h3>
          
          <div className="space-y-4">
            <div className="p-4 bg-green-500/10 rounded-lg border border-green-500/20">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">CO₂ Avoided This Month</span>
                <Badge variant="outline" className="text-green-600">
                  +{(detailedMetrics.environmental.co2Avoided.current - detailedMetrics.environmental.co2Avoided.previous).toFixed(1)} tons
                </Badge>
              </div>
              <div className="text-2xl font-mono font-bold text-green-500 mb-1">
                {detailedMetrics.environmental.co2Avoided.current} tons
              </div>
              <div className="text-xs text-muted-foreground">
                Annual projection: {detailedMetrics.environmental.co2Avoided.annual} tons
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div className="p-3 bg-muted/30 rounded-lg">
                <div className="text-xs text-muted-foreground font-medium mb-1">Trees Equivalent</div>
                <div className="font-bold text-lg">{detailedMetrics.environmental.treesEquivalent.current}</div>
                <div className="text-xs text-green-600">+{detailedMetrics.environmental.treesEquivalent.current - detailedMetrics.environmental.treesEquivalent.previous}</div>
              </div>
              
              <div className="p-3 bg-muted/30 rounded-lg">
                <div className="text-xs text-muted-foreground font-medium mb-1">Coal Avoided (lbs)</div>
                <div className="font-bold text-lg">{detailedMetrics.environmental.coalAvoided.current.toLocaleString()}</div>
                <div className="text-xs text-green-600">+{(detailedMetrics.environmental.coalAvoided.current - detailedMetrics.environmental.coalAvoided.previous).toLocaleString()}</div>
              </div>
            </div>
          </div>
        </Card>

        {/* Interactive Advanced Charts */}
        <Tabs defaultValue="performance" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="comparison">Comparison</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="forecast">AI Forecast</TabsTrigger>
          </TabsList>

          <TabsContent value="performance">
            <Card className="solar-card p-6">
              <h3 className="font-heading font-semibold mb-4">System Performance</h3>
              <EnergyChart />
              
              <div className="mt-6 space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Performance Ratio</span>
                  <span className="font-mono font-bold text-primary">
                    {detailedMetrics.technical.pr.current}%
                  </span>
                </div>
                <Progress value={detailedMetrics.technical.pr.current} className="h-2" />
                
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">System Availability</span>
                  <span className="font-mono font-bold text-green-500">
                    {detailedMetrics.technical.availability.current}%
                  </span>
                </div>
                <Progress value={detailedMetrics.technical.availability.current} className="h-2" />
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="comparison">
            <Card className="solar-card p-6">
              <h3 className="font-heading font-semibold mb-4">Comparative Analysis</h3>
              
              <div className="space-y-4">
                <div className="p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">vs Neighborhood</span>
                    <Badge variant="outline">78th percentile</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Your: {detailedMetrics.comparative.neighbors.userValue} kWh</span>
                    <span className="text-sm">Avg: {detailedMetrics.comparative.neighbors.average} kWh</span>
                  </div>
                </div>

                <div className="p-4 bg-green-500/10 rounded-lg border border-green-500/20">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">vs Regional Average</span>
                    <Badge variant="outline">85th percentile</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Your: {detailedMetrics.comparative.region.userValue} kWh</span>
                    <span className="text-sm">Avg: {detailedMetrics.comparative.region.average} kWh</span>
                  </div>
                </div>

                <div className="p-4 bg-purple-500/10 rounded-lg border border-purple-500/20">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">vs Optimal Performance</span>
                    <Badge variant="outline">{detailedMetrics.comparative.optimal.efficiency}% efficiency</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Actual: {detailedMetrics.comparative.optimal.actual} kWh</span>
                    <span className="text-sm">Optimal: {detailedMetrics.comparative.optimal.theoretical} kWh</span>
                  </div>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="trends">
            <Card className="solar-card p-6">
              <h3 className="font-heading font-semibold mb-4">Long-term Trends</h3>
              
              <div className="space-y-4">
                <div className="p-4 bg-muted/30 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">Annual Degradation</span>
                    <Badge variant={detailedMetrics.technical.degradation.annual < detailedMetrics.technical.degradation.expected ? "default" : "secondary"}>
                      {detailedMetrics.technical.degradation.annual}%/year
                    </Badge>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Expected: {detailedMetrics.technical.degradation.expected}%/year
                  </div>
                </div>

                <div className="p-4 bg-muted/30 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">Cumulative Degradation</span>
                    <span className="font-mono font-bold">{detailedMetrics.technical.degradation.cumulative}%</span>
                  </div>
                  <Progress value={detailedMetrics.technical.degradation.cumulative} className="h-2" />
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="forecast">
            <Card className="solar-card p-6">
              <h3 className="font-heading font-semibold mb-4 flex items-center gap-2">
                <Target className="w-5 h-5" />
                AI-Powered Forecasting
              </h3>
              
              <div className="space-y-3">
                <div className="p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg border border-blue-500/20">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">Next Month Projection</span>
                    <Badge variant="outline">92% confidence</Badge>
                  </div>
                  <div className="text-2xl font-mono font-bold text-primary">345 kWh</div>
                  <div className="text-xs text-muted-foreground">Based on weather & historical data</div>
                </div>

                <div className="p-4 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg border border-green-500/20">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">Annual Savings Forecast</span>
                    <Badge variant="outline">AI Model</Badge>
                  </div>
                  <div className="text-2xl font-mono font-bold text-green-500">$3,247</div>
                  <div className="text-xs text-muted-foreground">Machine learning prediction</div>
                </div>
              </div>
              
              <div className="mt-4 p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                <div className="flex items-center gap-2 text-yellow-600 mb-2">
                  <Zap className="w-4 h-4" />
                  <span className="font-medium text-sm">Optimization Opportunity</span>
                </div>
                <p className="text-xs text-yellow-600">
                  Analysis shows 15% improvement potential with minor system adjustments. Schedule a consultation for optimization recommendations.
                </p>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};