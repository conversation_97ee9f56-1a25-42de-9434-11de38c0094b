import { Sun, TrendingUp, Zap } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

export const EnergyProductionCard = () => {
  const todayProduction = 28.5;
  const todayTarget = 35;
  const currentPower = 4.2;
  const peakPower = 6.8;
  const efficiency = 94;

  return (
    <Card className="energy-card p-6 relative overflow-hidden">
      <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-solar opacity-20 rounded-full -translate-y-6 translate-x-6 rotating-sun"></div>
      
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-primary/20 rounded-xl shadow-glow">
            <Sun className="w-6 h-6 text-primary pulse-glow" />
          </div>
          <div>
            <h3 className="font-heading font-bold text-card-foreground">Energy Production</h3>
            <p className="text-sm text-muted-foreground">Today's Performance</p>
          </div>
        </div>
        <div className="text-right">
          <div className="status-optimal">
            <TrendingUp className="w-3 h-3 inline mr-1" />
            {efficiency}%
          </div>
        </div>
      </div>
      
      <div className="space-y-6">
        {/* Current Power */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Zap className="w-4 h-4 text-primary energy-flow" />
            <span className="text-sm font-medium text-muted-foreground">Live Power</span>
          </div>
          <div className="text-right">
            <div className="text-2xl font-mono font-bold text-primary">
              {currentPower} <span className="text-lg text-muted-foreground">kW</span>
            </div>
            <div className="text-xs text-muted-foreground">Peak: {peakPower} kW</div>
          </div>
        </div>

        {/* Today's Production */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-muted-foreground">Today's Generation</span>
            <span className="text-lg font-bold text-primary">
              {todayProduction} / {todayTarget} kWh
            </span>
          </div>
          <Progress 
            value={(todayProduction / todayTarget) * 100} 
            className="h-3 bg-muted/50" 
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>81% of daily target</span>
            <span>{todayTarget - todayProduction} kWh remaining</span>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-4 pt-2 border-t border-border/50">
          <div className="text-center">
            <div className="text-lg font-bold text-secondary">847</div>
            <div className="text-xs text-muted-foreground">kWh this month</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-accent">98%</div>
            <div className="text-xs text-muted-foreground">System uptime</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-primary">$284</div>
            <div className="text-xs text-muted-foreground">Savings MTD</div>
          </div>
        </div>
      </div>
    </Card>
  );
};