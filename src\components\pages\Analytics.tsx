import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { BarChart3, Download, TrendingUp, Calendar, Zap, Battery, Sun, DollarSign } from "lucide-react";
import { EnergyChart } from "../charts/EnergyChart";
import { exportToPDF, exportToCSV } from "@/utils/exportUtils";
import { useToast } from "@/hooks/use-toast";

export const Analytics = () => {
  const { toast } = useToast();
  
  const weeklyData = [
    { day: "Mon", energy: 32.4, savings: 18.50 },
    { day: "Tue", energy: 28.7, savings: 16.20 },
    { day: "Wed", energy: 35.2, savings: 20.10 },
    { day: "Thu", energy: 31.8, savings: 18.00 },
    { day: "Fri", energy: 29.6, savings: 16.80 },
    { day: "Sat", energy: 33.1, savings: 19.20 },
    { day: "Sun", energy: 30.9, savings: 17.60 },
  ];

  const systemData = {
    currentPower: '3.2',
    dailyEnergy: '28.4',
    monthlyEnergy: '847.3',
    efficiency: '94',
    monthlySavings: '284',
    totalSavings: '3420',
    co2Saved: '2.4',
    treesEquivalent: '32',
    waterSaved: '1240',
    batteryHealth: '97',
    paybackProgress: '32',
    panelStatus: 'Optimal',
    inverterStatus: 'Optimal',
    connectionStatus: 'Connected'
  };

  const handleExportPDF = () => {
    const result = exportToPDF(systemData);
    toast({
      title: result.success ? "Export Successful" : "Export Failed",
      description: result.message,
      variant: result.success ? "default" : "destructive",
    });
  };

  const handleExportCSV = () => {
    const result = exportToCSV(systemData);
    toast({
      title: result.success ? "Export Successful" : "Export Failed", 
      description: result.message,
      variant: result.success ? "default" : "destructive",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-bg">
      <div className="p-4 pb-24 space-y-6 max-w-md mx-auto custom-scrollbar">
        {/* Header */}
        <div className="text-center py-4">
          <h1 className="text-hero font-heading font-bold bg-gradient-solar bg-clip-text text-transparent mb-2">
            Analytics
          </h1>
          <p className="text-muted-foreground">Deep insights into your solar performance</p>
        </div>

        {/* Key Metrics Grid */}
        <div className="grid grid-cols-2 gap-4">
          <Card className="energy-card p-4 text-center">
            <Sun className="w-6 h-6 text-primary mx-auto mb-2" />
            <div className="text-xl font-bold text-primary">847.3</div>
            <div className="text-xs text-muted-foreground">kWh this month</div>
            <div className="text-xs text-success mt-1">+12% vs last month</div>
          </Card>
          
          <Card className="power-card p-4 text-center">
            <DollarSign className="w-6 h-6 text-accent mx-auto mb-2" />
            <div className="text-xl font-bold text-accent">$284</div>
            <div className="text-xs text-muted-foreground">Savings MTD</div>
            <div className="text-xs text-success mt-1">+8% vs last month</div>
          </Card>
          
          <Card className="eco-card p-4 text-center">
            <Battery className="w-6 h-6 text-secondary mx-auto mb-2" />
            <div className="text-xl font-bold text-secondary">94%</div>
            <div className="text-xs text-muted-foreground">Avg efficiency</div>
            <div className="text-xs text-success mt-1">+2% vs last month</div>
          </Card>
          
          <Card className="solar-card p-4 text-center">
            <Zap className="w-6 h-6 text-warning mx-auto mb-2" />
            <div className="text-xl font-bold text-warning">2.4</div>
            <div className="text-xs text-muted-foreground">tons CO₂ saved</div>
            <div className="text-xs text-success mt-1">+15% vs last month</div>
          </Card>
        </div>

        {/* Interactive Charts */}
        <Tabs defaultValue="daily" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="daily">Daily</TabsTrigger>
            <TabsTrigger value="weekly">Weekly</TabsTrigger>
            <TabsTrigger value="monthly">Monthly</TabsTrigger>
            <TabsTrigger value="yearly">Yearly</TabsTrigger>
          </TabsList>
          
          <TabsContent value="daily" className="space-y-4">
            <Card className="solar-card p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <BarChart3 className="w-6 h-6 text-primary" />
                  <h3 className="font-heading font-semibold text-card-foreground">Today's Power Generation</h3>
                </div>
                <div className="status-optimal">Live</div>
              </div>
              <EnergyChart />
            </Card>
          </TabsContent>
          
          <TabsContent value="weekly" className="space-y-4">
            <Card className="solar-card p-6">
              <div className="flex items-center gap-3 mb-6">
                <TrendingUp className="w-6 h-6 text-secondary" />
                <h3 className="font-heading font-semibold text-card-foreground">Weekly Performance</h3>
              </div>
              
              <div className="space-y-3">
                {weeklyData.map((day, index) => (
                  <div key={day.day} className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center">
                        <span className="text-xs font-bold text-primary">{day.day}</span>
                      </div>
                      <div>
                        <div className="text-sm font-medium">{day.energy} kWh</div>
                        <div className="text-xs text-muted-foreground">${day.savings} saved</div>
                      </div>
                    </div>
                    <div className="w-16 h-2 bg-muted/50 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-gradient-solar rounded-full transition-all duration-500"
                        style={{ width: `${(day.energy / 40) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </TabsContent>
          
          <TabsContent value="monthly" className="space-y-4">
            <Card className="solar-card p-6">
              <div className="text-center py-8">
                <Calendar className="w-16 h-16 mx-auto mb-4 text-primary opacity-50" />
                <h3 className="font-heading font-semibold text-card-foreground mb-2">Monthly Analytics</h3>
                <p className="text-muted-foreground">Advanced monthly breakdowns coming soon</p>
              </div>
            </Card>
          </TabsContent>
          
          <TabsContent value="yearly" className="space-y-4">
            <Card className="solar-card p-6">
              <div className="text-center py-8">
                <TrendingUp className="w-16 h-16 mx-auto mb-4 text-accent opacity-50" />
                <h3 className="font-heading font-semibold text-card-foreground mb-2">Yearly Trends</h3>
                <p className="text-muted-foreground">Annual performance insights coming soon</p>
              </div>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Export Options */}
        <div className="space-y-3">
          <Button 
            className="w-full btn-solar" 
            onClick={() => handleExportPDF()}
          >
            <Download className="w-4 h-4 mr-2" />
            Export Detailed Report (PDF)
          </Button>
          <Button 
            variant="outline" 
            className="w-full"
            onClick={() => handleExportCSV()}
          >
            <Download className="w-4 h-4 mr-2" />
            Export Data (CSV)
          </Button>
        </div>
      </div>
    </div>
  );
};