import { <PERSON>u, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, MoreVertical } from "lucide-react";
import { Button } from "@/components/ui/button";
import { NotificationCenter } from "@/components/ui/notification-center";
import { useRealTimeData } from "@/hooks/useRealTimeData";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from "@/components/ui/dropdown-menu";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useState } from "react";

interface TopHeaderProps {
  title: string;
  onNavigate?: (page: string) => void;
}

export const TopHeader = ({ title, onNavigate }: TopHeaderProps) => {
  const { alerts, markAlertAsRead, clearAlert } = useRealTimeData();
  const { toast } = useToast();
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const handleMenuAction = (action: string) => {
    switch (action) {
      case "search":
        setSearchOpen(true);
        break;
      case "settings":
        onNavigate?.("settings");
        break;
      case "profile":
        onNavigate?.("profile");
        break;
      case "help":
        toast({
          title: "Help Center",
          description: "Help documentation is now available.",
        });
        break;
      case "logout":
        toast({
          title: "Signed Out",
          description: "You have been successfully signed out.",
        });
        break;
      default:
        break;
    }
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      toast({
        title: "Search Results",
        description: `Searching for: ${searchQuery}`,
      });
      setSearchOpen(false);
      setSearchQuery("");
    }
  };

  return (
    <header className="bg-card/90 backdrop-blur-lg border-b border-border/50 px-4 py-3 sticky top-0 z-50 shadow-sm">
      <div className="flex items-center justify-between max-w-md mx-auto">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="text-muted-foreground hover:text-foreground hover:bg-accent/50">
              <Menu className="w-5 h-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-56">
            <DropdownMenuLabel>Quick Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => handleMenuAction("search")}>
              <Search className="mr-2 h-4 w-4" />
              Search System
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleMenuAction("settings")}>
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleMenuAction("help")}>
              <Bell className="mr-2 h-4 w-4" />
              Help Center
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        
        <div className="text-center">
          <h1 className="font-heading font-bold text-lg text-foreground">{title}</h1>
          <div className="w-8 h-0.5 bg-gradient-solar mx-auto mt-1 rounded-full"></div>
        </div>
        
        <div className="flex items-center gap-1">
          <NotificationCenter 
            notifications={alerts}
            onMarkAsRead={markAlertAsRead}
            onClear={clearAlert}
          />
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="text-muted-foreground hover:text-foreground hover:bg-accent/50">
                <User className="w-5 h-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>John Solar</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleMenuAction("profile")}>
                <User className="mr-2 h-4 w-4" />
                View Profile
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleMenuAction("settings")}>
                <Settings className="mr-2 h-4 w-4" />
                Account Settings
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleMenuAction("logout")}>
                Logout
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Dialog open={searchOpen} onOpenChange={setSearchOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Search Solar System</DialogTitle>
          </DialogHeader>
          <div className="flex gap-2">
            <Input
              placeholder="Search metrics, alerts, insights..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && handleSearch()}
            />
            <Button onClick={handleSearch}>Search</Button>
          </div>
        </DialogContent>
      </Dialog>
    </header>
  );
};