import { useState } from "react";
import { Settings, User, Shield, Bell, Pa<PERSON>, Download, Info, LogOut } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useTheme } from "@/hooks/use-theme";
import { useToast } from "@/hooks/use-toast";
import { ProfessionalButton } from "@/components/ui/professional-button";

export const EnhancedSettings = () => {
  const { theme, setTheme } = useTheme();
  const { toast } = useToast();
  
  const [settings, setSettings] = useState({
    autoRefresh: true,
    dataCollection: true,
    weatherAlerts: true,
    maintenanceReminders: true,
    performanceReports: false,
    dataRetention: "1year",
    updateFrequency: "realtime",
  });

  const updateSetting = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    toast({
      title: "Settings Updated",
      description: `${key} has been updated successfully.`,
    });
  };

  return (
    <div className="min-h-screen bg-gradient-bg">
      <div className="p-4 pb-24 space-y-6 max-w-md mx-auto">
        {/* Header */}
        <Card className="solar-card p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 bg-gradient-premium rounded-xl">
              <Settings className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="font-heading font-bold text-xl">Settings</h1>
              <p className="text-sm text-muted-foreground">Customize your experience</p>
            </div>
          </div>
        </Card>

        {/* Settings Tabs */}
        <Tabs defaultValue="general" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="privacy">Privacy</TabsTrigger>
            <TabsTrigger value="notifications">Alerts</TabsTrigger>
            <TabsTrigger value="about">About</TabsTrigger>
          </TabsList>

          <TabsContent value="general">
            <div className="space-y-4">
              <Card className="solar-card p-6">
                <h3 className="font-heading font-semibold mb-4 flex items-center gap-2">
                  <Palette className="w-5 h-5" />
                  Appearance
                </h3>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-sm">Theme</div>
                      <div className="text-xs text-muted-foreground">Choose your preferred theme</div>
                    </div>
                    <Select value={theme} onValueChange={setTheme}>
                      <SelectTrigger className="w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">Light</SelectItem>
                        <SelectItem value="dark">Dark</SelectItem>
                        <SelectItem value="system">System</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-sm">Auto Refresh</div>
                      <div className="text-xs text-muted-foreground">Update data automatically</div>
                    </div>
                    <Switch
                      checked={settings.autoRefresh}
                      onCheckedChange={(value) => updateSetting('autoRefresh', value)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-sm">Update Frequency</div>
                      <div className="text-xs text-muted-foreground">How often to refresh data</div>
                    </div>
                    <Select value={settings.updateFrequency} onValueChange={(value) => updateSetting('updateFrequency', value)}>
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="realtime">Real-time</SelectItem>
                        <SelectItem value="1min">1 minute</SelectItem>
                        <SelectItem value="5min">5 minutes</SelectItem>
                        <SelectItem value="15min">15 minutes</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="privacy">
            <div className="space-y-4">
              <Card className="solar-card p-6">
                <h3 className="font-heading font-semibold mb-4 flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  Privacy & Data
                </h3>
                
                <div className="space-y-4">
                  {[
                    { key: 'dataCollection', label: 'Data Collection', description: 'Allow anonymous usage analytics' },
                    { key: 'performanceReports', label: 'Performance Reports', description: 'Generate monthly performance reports' },
                  ].map((setting) => (
                    <div key={setting.key} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                      <div>
                        <div className="font-medium text-sm">{setting.label}</div>
                        <div className="text-xs text-muted-foreground">{setting.description}</div>
                      </div>
                      <Switch
                        checked={settings[setting.key as keyof typeof settings] as boolean}
                        onCheckedChange={(value) => updateSetting(setting.key, value)}
                      />
                    </div>
                  ))}

                  <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                    <div>
                      <div className="font-medium text-sm">Data Retention</div>
                      <div className="text-xs text-muted-foreground">How long to keep your data</div>
                    </div>
                    <Select value={settings.dataRetention} onValueChange={(value) => updateSetting('dataRetention', value)}>
                      <SelectTrigger className="w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1month">1 Month</SelectItem>
                        <SelectItem value="6months">6 Months</SelectItem>
                        <SelectItem value="1year">1 Year</SelectItem>
                        <SelectItem value="forever">Forever</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="mt-6 grid grid-cols-2 gap-3">
                  <ProfessionalButton variant="outline">Export Data</ProfessionalButton>
                  <ProfessionalButton variant="outline">Delete Data</ProfessionalButton>
                </div>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="notifications">
            <div className="space-y-4">
              <Card className="solar-card p-6">
                <h3 className="font-heading font-semibold mb-4 flex items-center gap-2">
                  <Bell className="w-5 h-5" />
                  Notification Preferences
                </h3>
                
                <div className="space-y-4">
                  {[
                    { key: 'weatherAlerts', label: 'Weather Alerts', description: 'Get notified about weather affecting performance' },
                    { key: 'maintenanceReminders', label: 'Maintenance Reminders', description: 'Periodic maintenance notifications' },
                  ].map((setting) => (
                    <div key={setting.key} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                      <div>
                        <div className="font-medium text-sm">{setting.label}</div>
                        <div className="text-xs text-muted-foreground">{setting.description}</div>
                      </div>
                      <Switch
                        checked={settings[setting.key as keyof typeof settings] as boolean}
                        onCheckedChange={(value) => updateSetting(setting.key, value)}
                      />
                    </div>
                  ))}
                </div>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="about">
            <div className="space-y-4">
              <Card className="solar-card p-6">
                <h3 className="font-heading font-semibold mb-4 flex items-center gap-2">
                  <Info className="w-5 h-5" />
                  About Solar Dashboard
                </h3>
                
                <div className="space-y-4 text-center">
                  <div>
                    <div className="font-mono font-bold text-2xl text-primary">v2.1.0</div>
                    <div className="text-sm text-muted-foreground">Latest Version</div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <ProfessionalButton variant="outline">Check Updates</ProfessionalButton>
                    <ProfessionalButton variant="outline">Release Notes</ProfessionalButton>
                    <ProfessionalButton variant="outline">Contact Support</ProfessionalButton>
                    <ProfessionalButton variant="outline">Privacy Policy</ProfessionalButton>
                  </div>

                  <div className="pt-4 border-t border-border/50">
                    <ProfessionalButton variant="outline" className="w-full text-red-500 hover:text-red-600">
                      <LogOut className="w-4 h-4 mr-2" />
                      Sign Out
                    </ProfessionalButton>
                  </div>
                </div>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};