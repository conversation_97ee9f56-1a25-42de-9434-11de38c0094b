import { EnhancedPowerCard } from "../dashboard/EnhancedPowerCard";
import { EnhancedBatteryCard } from "../dashboard/EnhancedBatteryCard";
import { GridFlowCard } from "../dashboard/GridFlowCard";
import { WeatherInsightsCard } from "../dashboard/WeatherInsightsCard";
import { QuickActions } from "../dashboard/QuickActions";
import { EnergyProductionCard } from "../dashboard/EnergyProductionCard";
import { EnvironmentalImpactCard } from "../dashboard/EnvironmentalImpactCard";
import { SavingsCalculatorCard } from "../dashboard/SavingsCalculatorCard";
import { SystemHealthCard } from "../dashboard/SystemHealthCard";
import { LiveMetricsCard } from "../dashboard/LiveMetricsCard";
import { SmartInsightsCard } from "../dashboard/SmartInsightsCard";
import { ProfessionalStatsGrid } from "../dashboard/ProfessionalStatsGrid";
import { AdvancedMetricsCard } from "../dashboard/AdvancedMetricsCard";
import { SmartAutomationCard } from "../dashboard/SmartAutomationCard";
import { useRealTimeData } from "@/hooks/useRealTimeData";
import { DashboardSkeleton } from "@/components/ui/loading-skeleton";

export const Dashboard = () => {
  const { data, isLoading } = useRealTimeData();

  if (isLoading) {
    return <DashboardSkeleton />;
  }

  return (
    <div className="min-h-screen bg-gradient-bg">
      <div className="p-4 pb-24 space-y-6 max-w-md mx-auto custom-scrollbar">
        {/* Hero Welcome Section */}
        <div className="text-center py-6 relative">
          <div className="absolute inset-0 bg-gradient-solar opacity-5 rounded-2xl"></div>
          <h1 className="text-hero font-heading font-bold bg-gradient-solar bg-clip-text text-transparent mb-2">
            Solar Dashboard
          </h1>
          <p className="text-muted-foreground">Power Your Future with Clean Energy</p>
          <div className="mt-4 flex items-center justify-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
              <span className="text-success font-medium">System Online</span>
            </div>
            <div className="text-muted-foreground">|</div>
            <div className="text-muted-foreground">Last updated: just now</div>
          </div>
        </div>

        {/* Enhanced Energy Production - Full Width */}
        <EnergyProductionCard />

        {/* Professional Stats Overview */}
        <ProfessionalStatsGrid />

        {/* Main Metrics Grid */}
        <div className="grid grid-cols-2 gap-4">
          <EnhancedPowerCard />
          <EnhancedBatteryCard />
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <GridFlowCard />
          <WeatherInsightsCard />
        </div>

        {/* Live System Monitoring */}
        <LiveMetricsCard />

        {/* Advanced Metrics & Automation */}
        <div className="space-y-4">
          <AdvancedMetricsCard />
          <SmartAutomationCard />
        </div>

        {/* AI-Powered Insights */}
        <SmartInsightsCard />

        {/* Financial & Environmental Impact */}
        <div className="space-y-4">
          <SavingsCalculatorCard />
          <EnvironmentalImpactCard />
        </div>

        {/* System Health */}
        <SystemHealthCard />

        {/* Enhanced Quick Actions */}
        <QuickActions />
      </div>
    </div>
  );
};