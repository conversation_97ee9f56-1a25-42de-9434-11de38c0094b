import { useState } from "react";
import { Bell, AlertTriangle, CheckCircle, Info, X, Filter, Search, Archive, Settings } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useRealTimeData } from "@/hooks/useRealTimeData";
import { useToast } from "@/hooks/use-toast";
import { ProfessionalButton } from "@/components/ui/professional-button";
import { formatDistanceToNow } from "date-fns";

export const EnhancedAlerts = () => {
  const { alerts, markAlertAsRead, clearAlert, addAlert } = useRealTimeData();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [sortBy, setSortBy] = useState("newest");
  
  // Alert preferences
  const [alertSettings, setAlertSettings] = useState({
    systemStatus: true,
    maintenance: true,
    performance: true,
    weather: true,
    financial: true,
    security: true,
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
  });

  // Sample historical alerts
  const [historicalAlerts] = useState([
    {
      id: "hist1",
      type: "warning" as const,
      title: "Inverter Temperature High",
      message: "Inverter #2 temperature reached 85°C. Check ventilation.",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24),
      read: true,
      resolved: true,
      category: "maintenance",
      severity: "medium",
    },
    {
      id: "hist2",
      type: "error" as const,
      title: "Grid Connection Lost",
      message: "System switched to battery backup due to grid outage.",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 48),
      read: true,
      resolved: true,
      category: "system",
      severity: "high",
    },
    {
      id: "hist3",
      type: "info" as const,
      title: "Firmware Update Available",
      message: "New firmware v2.1.3 is available for your system.",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 72),
      read: true,
      resolved: false,
      category: "system",
      severity: "low",
    },
  ]);

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'error': return <AlertTriangle className="w-5 h-5 text-red-500" />;
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'success': return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'info': return <Info className="w-5 h-5 text-blue-500" />;
      default: return <Info className="w-5 h-5 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-500/10 text-red-500 border-red-500/20';
      case 'medium': return 'bg-yellow-500/10 text-yellow-500 border-yellow-500/20';
      case 'low': return 'bg-blue-500/10 text-blue-500 border-blue-500/20';
      default: return 'bg-gray-500/10 text-gray-500 border-gray-500/20';
    }
  };

  const filteredAlerts = alerts.filter(alert => {
    if (filterType !== "all" && alert.type !== filterType) return false;
    if (searchTerm && !alert.title.toLowerCase().includes(searchTerm.toLowerCase()) && 
        !alert.message.toLowerCase().includes(searchTerm.toLowerCase())) return false;
    return true;
  });

  const sortedAlerts = filteredAlerts.sort((a, b) => {
    if (sortBy === "newest") return b.timestamp.getTime() - a.timestamp.getTime();
    if (sortBy === "oldest") return a.timestamp.getTime() - b.timestamp.getTime();
    if (sortBy === "priority") {
      const priority = { error: 3, warning: 2, info: 1, success: 0 };
      return priority[b.type] - priority[a.type];
    }
    return 0;
  });

  const unreadCount = alerts.filter(alert => !alert.read).length;

  const createTestAlert = () => {
    const testAlerts = [
      {
        type: "warning" as const,
        title: "Panel Cleaning Recommended",
        message: "Efficiency dropped by 5%. Consider cleaning solar panels.",
        read: false,
      },
      {
        type: "info" as const,
        title: "Peak Generation Today",
        message: "Your system generated 8.2 kWh, exceeding daily target!",
        read: false,
      },
      {
        type: "error" as const,
        title: "Inverter Offline",
        message: "Inverter #1 is not responding. Check connection.",
        read: false,
      },
    ];
    
    const randomAlert = testAlerts[Math.floor(Math.random() * testAlerts.length)];
    addAlert(randomAlert);
    
    toast({
      title: "Test Alert Created",
      description: `Added: ${randomAlert.title}`,
    });
  };

  const markAllAsRead = () => {
    alerts.forEach(alert => {
      if (!alert.read) {
        markAlertAsRead(alert.id);
      }
    });
    toast({
      title: "All alerts marked as read",
      description: `${unreadCount} alerts updated`,
    });
  };

  const updateAlertSetting = (setting: string, value: boolean) => {
    setAlertSettings(prev => ({ ...prev, [setting]: value }));
    toast({
      title: "Settings Updated",
      description: `${setting} notifications ${value ? 'enabled' : 'disabled'}`,
    });
  };

  return (
    <div className="min-h-screen bg-gradient-bg">
      <div className="p-4 pb-24 space-y-6 max-w-md mx-auto">
        {/* Header */}
        <Card className="solar-card p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-gradient-premium rounded-xl">
                <Bell className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="font-heading font-bold text-xl">Alert Center</h1>
                <p className="text-sm text-muted-foreground">
                  {unreadCount} unread notifications
                </p>
              </div>
            </div>
            {unreadCount > 0 && (
              <Badge variant="destructive" className="px-3 py-1">
                {unreadCount}
              </Badge>
            )}
          </div>

          <div className="grid grid-cols-3 gap-3">
            <div className="text-center p-3 bg-muted/30 rounded-lg">
              <div className="font-mono font-bold text-lg text-red-500">
                {alerts.filter(a => a.type === 'error').length}
              </div>
              <div className="text-xs text-muted-foreground">Critical</div>
            </div>
            <div className="text-center p-3 bg-muted/30 rounded-lg">
              <div className="font-mono font-bold text-lg text-yellow-500">
                {alerts.filter(a => a.type === 'warning').length}
              </div>
              <div className="text-xs text-muted-foreground">Warning</div>
            </div>
            <div className="text-center p-3 bg-muted/30 rounded-lg">
              <div className="font-mono font-bold text-lg text-blue-500">
                {alerts.filter(a => a.type === 'info').length}
              </div>
              <div className="text-xs text-muted-foreground">Info</div>
            </div>
          </div>
        </Card>

        {/* Controls */}
        <Card className="solar-card p-4">
          <div className="space-y-3">
            <div className="flex gap-2">
              <div className="flex-1">
                <Input
                  placeholder="Search alerts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="h-8"
                />
              </div>
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-24 h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="error">Error</SelectItem>
                  <SelectItem value="warning">Warning</SelectItem>
                  <SelectItem value="info">Info</SelectItem>
                  <SelectItem value="success">Success</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex gap-2">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="flex-1 h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="oldest">Oldest First</SelectItem>
                  <SelectItem value="priority">By Priority</SelectItem>
                </SelectContent>
              </Select>
              
              <ProfessionalButton variant="outline" size="sm" onClick={markAllAsRead}>
                Mark All Read
              </ProfessionalButton>
              
              <ProfessionalButton variant="outline" size="sm" onClick={createTestAlert}>
                Test Alert
              </ProfessionalButton>
            </div>
          </div>
        </Card>

        {/* Alert Tabs */}
        <Tabs defaultValue="active" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="active">
            <div className="space-y-3">
              {sortedAlerts.length === 0 ? (
                <Card className="solar-card p-8 text-center">
                  <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                  <h3 className="font-medium mb-2">All Clear!</h3>
                  <p className="text-sm text-muted-foreground">
                    No active alerts. Your system is running smoothly.
                  </p>
                </Card>
              ) : (
                sortedAlerts.map((alert) => (
                  <Card 
                    key={alert.id} 
                    className={`solar-card p-4 ${!alert.read ? 'border-primary/50 bg-primary/5' : ''}`}
                  >
                    <div className="flex items-start gap-3">
                      {getAlertIcon(alert.type)}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-medium text-sm truncate">{alert.title}</h3>
                          <div className="flex items-center gap-2">
                            {!alert.read && (
                              <Badge variant="secondary" className="text-xs">
                                New
                              </Badge>
                            )}
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => clearAlert(alert.id)}
                              className="h-6 w-6"
                            >
                              <X className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                        <p className="text-xs text-muted-foreground mb-3">
                          {alert.message}
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-muted-foreground">
                            {formatDistanceToNow(alert.timestamp, { addSuffix: true })}
                          </span>
                          {!alert.read && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => markAlertAsRead(alert.id)}
                              className="h-6 text-xs"
                            >
                              Mark Read
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="history">
            <div className="space-y-3">
              {historicalAlerts.map((alert) => (
                <Card key={alert.id} className="solar-card p-4 opacity-75">
                  <div className="flex items-start gap-3">
                    {getAlertIcon(alert.type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium text-sm truncate">{alert.title}</h3>
                        <div className="flex items-center gap-2">
                          <Badge className={`text-xs ${getSeverityColor(alert.severity)}`}>
                            {alert.severity}
                          </Badge>
                          {alert.resolved && (
                            <Badge variant="outline" className="text-xs text-green-500">
                              Resolved
                            </Badge>
                          )}
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground mb-3">
                        {alert.message}
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-muted-foreground">
                          {formatDistanceToNow(alert.timestamp, { addSuffix: true })}
                        </span>
                        <Badge variant="outline" className="text-xs">
                          {alert.category}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="settings">
            <div className="space-y-4">
              <Card className="solar-card p-6">
                <h3 className="font-heading font-semibold mb-4 flex items-center gap-2">
                  <Settings className="w-5 h-5" />
                  Alert Categories
                </h3>
                
                <div className="space-y-4">
                  {[
                    { key: 'systemStatus', label: 'System Status', description: 'Online/offline status changes' },
                    { key: 'maintenance', label: 'Maintenance', description: 'Cleaning, repairs, and servicing' },
                    { key: 'performance', label: 'Performance', description: 'Efficiency and generation alerts' },
                    { key: 'weather', label: 'Weather', description: 'Weather-related notifications' },
                    { key: 'financial', label: 'Financial', description: 'Savings and billing information' },
                    { key: 'security', label: 'Security', description: 'System security events' },
                  ].map((setting) => (
                    <div key={setting.key} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                      <div>
                        <div className="font-medium text-sm">{setting.label}</div>
                        <div className="text-xs text-muted-foreground">{setting.description}</div>
                      </div>
                      <Switch
                        checked={alertSettings[setting.key as keyof typeof alertSettings]}
                        onCheckedChange={(value) => updateAlertSetting(setting.key, value)}
                      />
                    </div>
                  ))}
                </div>
              </Card>

              <Card className="solar-card p-6">
                <h3 className="font-heading font-semibold mb-4">Notification Methods</h3>
                
                <div className="space-y-4">
                  {[
                    { key: 'emailNotifications', label: 'Email Notifications', description: 'Send alerts to your email' },
                    { key: 'pushNotifications', label: 'Push Notifications', description: 'Browser push notifications' },
                    { key: 'smsNotifications', label: 'SMS Notifications', description: 'Text message alerts' },
                  ].map((setting) => (
                    <div key={setting.key} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                      <div>
                        <div className="font-medium text-sm">{setting.label}</div>
                        <div className="text-xs text-muted-foreground">{setting.description}</div>
                      </div>
                      <Switch
                        checked={alertSettings[setting.key as keyof typeof alertSettings]}
                        onCheckedChange={(value) => updateAlertSetting(setting.key, value)}
                      />
                    </div>
                  ))}
                </div>
              </Card>

              <Card className="solar-card p-6">
                <h3 className="font-heading font-semibold mb-4">Quick Actions</h3>
                
                <div className="grid grid-cols-2 gap-3">
                  <ProfessionalButton variant="outline" onClick={createTestAlert}>
                    Test Alerts
                  </ProfessionalButton>
                  <ProfessionalButton variant="outline" onClick={markAllAsRead}>
                    Clear All
                  </ProfessionalButton>
                  <ProfessionalButton variant="outline">
                    Export History
                  </ProfessionalButton>
                  <ProfessionalButton variant="outline">
                    Reset Settings
                  </ProfessionalButton>
                </div>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};