import { useState } from "react";
import { Activity, Zap, TrendingUp, Clock, Target, Cpu } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useRealTimeData } from "@/hooks/useRealTimeData";

export const AdvancedMetricsCard = () => {
  const { data } = useRealTimeData();
  const [selectedMetric, setSelectedMetric] = useState("performance");

  const performanceData = {
    realtime: {
      voltage: 240.5,
      current: 12.3,
      frequency: 60.0,
      powerFactor: 0.95,
      temperature: 35.2,
      efficiency: data.efficiency,
    },
    historical: {
      hourly: [2.1, 3.5, 4.2, 5.8, 6.1, 7.2, 6.8, 5.4, 3.9, 2.7, 1.8, 0.9],
      daily: [45.2, 52.1, 38.7, 61.3, 48.9, 55.4, 42.6],
      monthly: [1420, 1680, 1550, 1810, 1640, 1910, 1760],
    },
    predictions: {
      nextHour: 6.8,
      today: 48.5,
      thisWeek: 312.4,
      confidence: 87,
    }
  };

  const systemHealth = {
    inverter: { status: "Optimal", health: 98, temp: 42 },
    panels: { status: "Excellent", health: 96, efficiency: 22.1 },
    battery: { status: "Good", health: 94, cycles: 1247 },
    grid: { status: "Connected", health: 100, voltage: 240.5 },
  };

  return (
    <Card className="solar-card p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Activity className="w-6 h-6 text-primary" />
          <h3 className="font-heading font-bold text-xl">Advanced Metrics</h3>
        </div>
        <Badge variant="secondary" className="animate-pulse">Live</Badge>
      </div>

      <Tabs value={selectedMetric} onValueChange={setSelectedMetric} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="realtime">Real-time</TabsTrigger>
          <TabsTrigger value="health">Health</TabsTrigger>
          <TabsTrigger value="forecast">Forecast</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="p-4 bg-muted/30 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="w-4 h-4 text-yellow-500" />
                <span className="text-sm font-medium">Power Output</span>
              </div>
              <div className="text-2xl font-mono font-bold text-primary">
                {data.currentPower.toFixed(1)} kW
              </div>
              <Progress value={75} className="mt-2" />
            </div>

            <div className="p-4 bg-muted/30 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="w-4 h-4 text-green-500" />
                <span className="text-sm font-medium">Efficiency</span>
              </div>
              <div className="text-2xl font-mono font-bold text-green-500">
                {performanceData.realtime.efficiency.toFixed(1)}%
              </div>
              <Progress value={performanceData.realtime.efficiency} className="mt-2" />
            </div>

            <div className="p-4 bg-muted/30 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="w-4 h-4 text-blue-500" />
                <span className="text-sm font-medium">Uptime</span>
              </div>
              <div className="text-2xl font-mono font-bold text-blue-500">
                99.8%
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                Last 30 days
              </div>
            </div>

            <div className="p-4 bg-muted/30 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Target className="w-4 h-4 text-purple-500" />
                <span className="text-sm font-medium">Target</span>
              </div>
              <div className="text-2xl font-mono font-bold text-purple-500">
                112%
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                vs Monthly Goal
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Today's Progress</span>
              <span className="font-mono">{data.dailyGeneration.toFixed(1)} / 50.0 kWh</span>
            </div>
            <Progress value={(data.dailyGeneration / 50) * 100} className="h-2" />
          </div>
        </TabsContent>

        <TabsContent value="realtime" className="space-y-4">
          <div className="grid grid-cols-1 gap-3">
            {Object.entries(performanceData.realtime).map(([key, value]) => (
              <div key={key} className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                <span className="text-sm font-medium capitalize">
                  {key.replace(/([A-Z])/g, ' $1').trim()}
                </span>
                <span className="font-mono font-bold">
                  {typeof value === 'number' ? value.toFixed(1) : value}
                  {key === 'voltage' && ' V'}
                  {key === 'current' && ' A'}
                  {key === 'frequency' && ' Hz'}
                  {key === 'temperature' && ' °C'}
                  {key === 'efficiency' && '%'}
                </span>
              </div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="health" className="space-y-4">
          <div className="space-y-3">
            {Object.entries(systemHealth).map(([component, info]) => (
              <div key={component} className="p-4 bg-muted/30 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Cpu className="w-4 h-4" />
                    <span className="font-medium capitalize">{component}</span>
                  </div>
                  <Badge 
                    variant={info.health > 95 ? "default" : info.health > 90 ? "secondary" : "destructive"}
                  >
                    {info.status}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Health: {info.health}%</span>
                  <Progress value={info.health} className="w-20 h-2" />
                </div>
              </div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="forecast" className="space-y-4">
          <div className="grid grid-cols-1 gap-3">
            <div className="p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg border border-blue-500/20">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">Next Hour Prediction</span>
                <Badge variant="outline">{performanceData.predictions.confidence}% confidence</Badge>
              </div>
              <div className="text-2xl font-mono font-bold text-primary">
                {performanceData.predictions.nextHour} kW
              </div>
            </div>

            <div className="p-4 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg border border-green-500/20">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">Today's Forecast</span>
                <Badge variant="outline">AI Powered</Badge>
              </div>
              <div className="text-2xl font-mono font-bold text-green-500">
                {performanceData.predictions.today} kWh
              </div>
            </div>

            <div className="p-4 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg border border-purple-500/20">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">Weekly Forecast</span>
                <Badge variant="outline">ML Model</Badge>
              </div>
              <div className="text-2xl font-mono font-bold text-purple-500">
                {performanceData.predictions.thisWeek} kWh
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </Card>
  );
};