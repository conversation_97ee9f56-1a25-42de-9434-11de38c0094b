import { useState, useEffect } from 'react';

interface SolarData {
  currentPower: number;
  batteryLevel: number;
  batteryCharging: boolean;
  gridFlow: number;
  dailyGeneration: number;
  weeklyGeneration: number;
  monthlyGeneration: number;
  efficiency: number;
  weather: {
    condition: string;
    temperature: number;
    humidity: number;
    uvIndex: number;
  };
  systemHealth: number;
  co2Saved: number;
  moneySaved: number;
  peakPower: number;
  averagePower: number;
  totalUptime: number;
}

interface Alert {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
}

export const useRealTimeData = () => {
  const [data, setData] = useState<SolarData>({
    currentPower: 4.2,
    batteryLevel: 78,
    batteryCharging: true,
    gridFlow: -1.8,
    dailyGeneration: 32.5,
    weeklyGeneration: 245.8,
    monthlyGeneration: 1024.3,
    efficiency: 94.2,
    weather: {
      condition: 'sunny',
      temperature: 24,
      humidity: 45,
      uvIndex: 8,
    },
    systemHealth: 97,
    co2Saved: 15.2,
    moneySaved: 12.45,
    peakPower: 5.8,
    averagePower: 3.1,
    totalUptime: 99.8,
  });

  const [alerts, setAlerts] = useState<Alert[]>([
    {
      id: '1',
      type: 'success',
      title: 'Peak Performance',
      message: 'Your system is operating at peak efficiency today!',
      timestamp: new Date(),
      read: false,
    },
    {
      id: '2',
      type: 'info',
      title: 'Weather Update',
      message: 'Sunny conditions expected for the next 3 days. Optimal generation ahead!',
      timestamp: new Date(Date.now() - 1000 * 60 * 30),
      read: false,
    },
  ]);

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate initial loading
    const loadingTimer = setTimeout(() => {
      setIsLoading(false);
    }, 2000);

    // Simulate real-time data updates
    const interval = setInterval(() => {
      setData(prevData => ({
        ...prevData,
        currentPower: Math.max(0, prevData.currentPower + (Math.random() - 0.5) * 0.5),
        batteryLevel: Math.min(100, Math.max(0, prevData.batteryLevel + (Math.random() - 0.5) * 2)),
        batteryCharging: Math.random() > 0.3,
        gridFlow: prevData.gridFlow + (Math.random() - 0.5) * 0.3,
        dailyGeneration: prevData.dailyGeneration + Math.random() * 0.1,
        efficiency: Math.min(100, Math.max(80, prevData.efficiency + (Math.random() - 0.5) * 0.5)),
        weather: {
          ...prevData.weather,
          temperature: Math.max(15, Math.min(35, prevData.weather.temperature + (Math.random() - 0.5) * 0.5)),
          humidity: Math.max(20, Math.min(80, prevData.weather.humidity + (Math.random() - 0.5) * 2)),
        },
        systemHealth: Math.min(100, Math.max(85, prevData.systemHealth + (Math.random() - 0.5) * 0.2)),
      }));
    }, 3000);

    return () => {
      clearTimeout(loadingTimer);
      clearInterval(interval);
    };
  }, []);

  const addAlert = (alert: Omit<Alert, 'id' | 'timestamp'>) => {
    const newAlert: Alert = {
      ...alert,
      id: Date.now().toString(),
      timestamp: new Date(),
    };
    setAlerts(prev => [newAlert, ...prev]);
  };

  const markAlertAsRead = (id: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === id ? { ...alert, read: true } : alert
    ));
  };

  const clearAlert = (id: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== id));
  };

  return {
    data,
    alerts,
    isLoading,
    addAlert,
    markAlertAsRead,
    clearAlert,
  };
};