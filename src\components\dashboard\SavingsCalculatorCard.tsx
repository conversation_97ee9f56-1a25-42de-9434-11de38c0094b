import { DollarSign, TrendingUp, Calculator, PiggyBank } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

export const SavingsCalculatorCard = () => {
  const monthlyBill = 245;
  const billWithoutSolar = 389;
  const monthlySavings = billWithoutSolar - monthlyBill;
  const annualSavings = monthlySavings * 12;
  const systemCost = 18500;
  const totalSaved = 3480; // since installation
  const paybackProgress = (totalSaved / systemCost) * 100;
  const estimatedPaybackMonths = Math.ceil((systemCost - totalSaved) / monthlySavings);

  return (
    <Card className="power-card p-6 relative overflow-hidden">
      <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-energy opacity-15 rounded-full -translate-y-6 translate-x-6"></div>
      
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-accent/20 rounded-xl shadow-accent">
            <DollarSign className="w-6 h-6 text-accent" />
          </div>
          <div>
            <h3 className="font-heading font-bold text-card-foreground">Savings Calculator</h3>
            <p className="text-sm text-muted-foreground">Financial benefits tracking</p>
          </div>
        </div>
        <Calculator className="w-5 h-5 text-accent" />
      </div>
      
      <div className="space-y-6">
        {/* This Month's Savings */}
        <div className="text-center bg-accent/5 rounded-xl p-4">
          <div className="text-3xl font-mono font-bold text-accent mb-1">
            ${monthlySavings}
          </div>
          <div className="text-sm text-muted-foreground">Saved this month</div>
          <div className="mt-2 flex items-center justify-center gap-2 text-xs text-success">
            <TrendingUp className="w-3 h-3" />
            <span>37% vs last month</span>
          </div>
        </div>

        {/* Bill Comparison */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Current bill</span>
            <span className="font-bold text-success">${monthlyBill}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Without solar</span>
            <span className="font-bold text-danger line-through">${billWithoutSolar}</span>
          </div>
          <div className="h-px bg-border"></div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Monthly savings</span>
            <span className="font-bold text-primary">${monthlySavings}</span>
          </div>
        </div>

        {/* Payback Progress */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-muted-foreground">System payback</span>
            <span className="text-sm font-bold text-accent">{paybackProgress.toFixed(1)}%</span>
          </div>
          <Progress value={paybackProgress} className="h-3 bg-muted/50" />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>${totalSaved.toLocaleString()} saved so far</span>
            <span>{estimatedPaybackMonths} months remaining</span>
          </div>
        </div>

        {/* Annual Projection */}
        <div className="grid grid-cols-2 gap-4 pt-2 border-t border-border/50">
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <PiggyBank className="w-4 h-4 text-primary mr-1" />
            </div>
            <div className="text-lg font-bold text-primary">${annualSavings.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">Annual savings</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-secondary">25 yrs</div>
            <div className="text-xs text-muted-foreground">System warranty</div>
          </div>
        </div>
      </div>
    </Card>
  );
};