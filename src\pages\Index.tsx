import { useState } from "react";
import { TopHeader } from "@/components/TopHeader";
import { BottomNavigation } from "@/components/BottomNavigation";
import { Dashboard } from "@/components/pages/Dashboard";
import { Analytics } from "@/components/pages/EnhancedAnalytics";
import { Recommendations } from "@/components/pages/AdvancedRecommendations";
import { EnhancedAlerts } from "@/components/pages/EnhancedAlerts";
import { EnhancedSettings } from "@/components/pages/EnhancedSettings";

const Index = () => {
  const [activeTab, setActiveTab] = useState("dashboard");

  const getPageTitle = () => {
    switch (activeTab) {
      case "dashboard": return "Dashboard";
      case "analytics": return "Analytics";
      case "recommendations": return "Recommendations";
      case "alerts": return "Alerts";
      case "settings": return "Settings";
      default: return "Solar App";
    }
  };

  const renderActivePage = () => {
    switch (activeTab) {
      case "dashboard": return <Dashboard />;
      case "analytics": return <Analytics />;
      case "recommendations": return <Recommendations />;
      case "alerts": return <EnhancedAlerts />;
      case "settings": return <EnhancedSettings />;
      default: return <Dashboard />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <TopHeader title={getPageTitle()} onNavigate={setActiveTab} />
      
      <main className="relative">
        {renderActivePage()}
      </main>
      
      <BottomNavigation activeTab={activeTab} onTabChange={setActiveTab} />
    </div>
  );
};

export default Index;
